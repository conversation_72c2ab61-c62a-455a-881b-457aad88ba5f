<?php

namespace App\Admin\Controllers\CrmAdmin;

use App\Models\DepartmentModels\CrmUserModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Grid;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UsersController extends AdminController
{

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Crm Users';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        $grid = new Grid(new CrmUserModel);
        $grid->paginate(100);
        $grid->model()->where('id', '!=', 0)->orderBy('user_status', 'desc')->orderBy('id');

        $grid->column('id', 'CRM ID')->sortable();
        $grid->column('user_status', '状态')->using([0 => '禁用', 1 => '启用'])->label([1 => 'success', 0 => 'danger'])->filter([0 => '禁用', 1 => '启用']);
        $grid->column('is_admin', '是否前台管理员')->filter([0 => '否', 1 => '是'])->bool();
        $grid->column('ua_id', 'UA ID');
        $grid->column('user_code', '工号')->sortable();
        $grid->column('name', '用户姓名')->sortable();
        $grid->column('department.dept_name', '所属部门')->sortable();
        $grid->column('roles', '前台角色')->display(function ($roles) {
            $roles = array_map(function ($role) {
                return "<span class='label label-success'>{$role['use_name']}</span>";
            }, $roles);
            return join('&nbsp;', $roles);
        });
        $grid->column('permissions', '前台权限')->display(function ($permissions) {
            $permissions = array_map(function ($permission) {
                return "<span class='label label-success'>{$permission['name']}</span>";
            }, $permissions);
            return join('&nbsp;', $permissions);
        });

        $grid->column('backendRoles', '后台角色')->display(function ($roles) {
            $roles = array_map(function ($role) {
                return "<span class='label label-success'>{$role['name']}</span>";
            }, $roles);
            return join('&nbsp;', $roles);
        });
        $grid->column('backendPermissions', '后台权限')->display(function ($permissions) {
            $permissions = array_map(function ($permission) {
                return "<span class='label label-success'>{$permission['name']}</span>";
            }, $permissions);
            return join('&nbsp;', $permissions);
        });

        $grid->column('created_at', '首次登录时间');

        $grid->filter(function ($filter) {

            // 去掉默认的id过滤器
            $filter->disableIdFilter();

            // 在这里添加字段过滤器
            $filter->like('name', '员工姓名');
            $filter->like('user_code', '员工工号');
            $filter->like('ua_id', 'UA ID');
            $filter->like('id', 'ID');

        });

        //actions 相关
        $grid->actions(function ($actions) {

            // 去掉删除
            $actions->disableDelete();

            // 去掉编辑
            // $actions->disableEdit();
            // 去掉查看
            $actions->disableView();
        });

        //禁止创建新用户
        $grid->disableCreateButton();
        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();

        return $grid;
    }


    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form(): Form
    {
        $permissionModel = config('admin.database.permissions_model');
        $roleModel = config('admin.database.roles_model');

        $form = new Form(new CrmUserModel);

        $form->display('id', __('ID'));
        $form->display('ua_id', 'UA ID');
        $form->display('user_code', '工号');
        $form->display('name', '用户姓名');
        $form->select('user_status', '状态')->options([0 => '禁用', 1 => '启用']);
        $form->select('is_admin', '是否前台管理员')->options([0 => '否', 1 => '是']);
        $form->multipleSelect('roles', '前台角色')->options(Role::all()->pluck('use_name', 'id'));
        $form->multipleSelect('permissions', '前台权限')->options(Permission::all()->pluck('name', 'id'));

        $form->multipleSelect('backendRoles', '后台角色')->options($roleModel::all()->pluck('name', 'id'));
        $form->multipleSelect('backendPermissions', '后台权限')->options($permissionModel::all()->pluck('name', 'id'));


        $form->tools(function (Form\Tools $tools) {
            // 去掉`列表`按钮
//            $tools->disableList();

            // 去掉`删除`按钮
            $tools->disableDelete();

            // 去掉`查看`按钮
            $tools->disableView();
        });
        // 去掉`查看`checkbox
        $form->disableViewCheck();

        return $form;
    }

}
