<?php

namespace App\Admin\Controllers\CrmAdmin;

use App\Models\DepartmentModels\CrmDeptModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Grid;

class DepartController extends AdminController
{

    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = 'Crm Department';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid(): Grid
    {
        $grid = new Grid(new CrmDeptModel());
        $grid->paginate(20);
        $grid->model()->where('id', '!=', 0);

        $grid->column('id', 'CRM ID')->sortable();
        $grid->column('dept_name', '部门名称')->sortable();
        $grid->column('depth', '部门深度')->sortable();
        $grid->column('owner', '负责人')->display(function ($owner) {
            return $owner ? $owner['name'] . '(' . $owner['user_code'] . ')' : '';
        });
        $grid->column('is_deleted', '是否删除')->filter([0 => '否', 1 => '是'])->bool();
        $grid->column('is_mpg', '是否为原有mpg')->filter([0 => '否', 1 => '是'])->bool();
        $grid->column('menbers', '当前成员')->display(function ($menbers) {
            $result = [];
            foreach ($menbers as $menber) {
                if ($menber['user_status'] == 1) {
                    $name = $menber['name'] . '(' . $menber['user_code'] . ')';
                    $result[] = "<span class='label label-success'>{$name}</span>";
                }
            }
            return join('&nbsp;', $result);
        })->width(400);

        $grid->filter(function ($filter) {

            // 去掉默认的id过滤器
            $filter->disableIdFilter();

            // 在这里添加字段过滤器
            $filter->like('dept_name', '部门名称');
//            $filter->like('user_code', '员工工号');
//            $filter->like('ua_id', 'UA ID');
//            $filter->like('id', 'ID');

        });

        //actions 相关
        $grid->disableActions();
        //禁止创建
        $grid->disableCreateButton();
        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();

        return $grid;
    }

}
