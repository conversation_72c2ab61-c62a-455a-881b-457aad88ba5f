<?php

namespace App\Admin\Controllers\CrmProduct;

use App\Models\PublicModels\CrmSubjectModel;
use Encore\Admin\Controllers\AdminController;
use Encore\Admin\Form;
use Encore\Admin\Form\Tools;
use Encore\Admin\Grid;

class SubjectController extends AdminController
{
    /**
     * Title for current resource.
     *
     * @var string
     */
    protected $title = '科目';

    protected function grid(): Grid
    {
        $grid = new Grid(new CrmSubjectModel);
        $grid->column('id', __('Id'));
        $grid->column('name_cn', "科目中文名");
        $grid->column('name_en', "科目英文名");
        $grid->column('status', "状态")->using([1 => '启用', 0 => '禁用']);

        $grid->filter(function ($filter) {
            // 去掉默认的id过滤器
            $filter->disableIdFilter();
            $filter->like('name_cn', '科目中文名');
            $filter->like('name_en', '科目英文名');
        });

        //actions 相关
        $grid->actions(function ($actions) {
            // 去掉删除
            $actions->disableDelete();
            // 去掉编辑
            // $actions->disableEdit();
            // 去掉查看
            $actions->disableView();
        });

        //禁用导出
        $grid->disableExport();
        //禁用多选
        $grid->disableRowSelector();

        return $grid;
    }

    protected function form(): Form
    {
        $form = new Form(new CrmSubjectModel);
        $form->text('name_cn', "科目中文名")->required();
        $form->text('name_en', "科目英文名");
        $form->switch('status', "状态")->options([1 => '启用', 0 => '禁用'])->default(1);

        $form->tools(function (Tools $tools) {
            // 去掉`删除`按钮
            $tools->disableDelete();
            // 去掉`查看`按钮
            $tools->disableView();
        });

        $form->footer(function ($footer) {
            // 去掉`查看`checkbox
            $footer->disableViewCheck();
            // 去掉`继续编辑`checkbox
            $footer->disableEditingCheck();
        });

        return $form;
    }


}
