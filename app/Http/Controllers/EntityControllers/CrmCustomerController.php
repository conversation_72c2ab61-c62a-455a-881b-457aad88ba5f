<?php

namespace App\Http\Controllers\EntityControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\EntityService\CrmCustomerService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CrmCustomerController extends Controller
{

    public function index(Request $request): Factory|\Illuminate\Foundation\Application|View|JsonResponse|Application
    {
        $params = $request->all();
        $rules = [
            'page' => 'int',
            'pageNum' => 'int',
        ];
        $ruleMessages = [
            'page.int' => 'page is not of int type',
            'pageNum.int' => 'pageNum is not of int type',
        ];
        $valiResult = Validator::make($params, $rules, $ruleMessages);
        if ($valiResult->fails()) {
            return $this->response($valiResult->errors()->first());
        }
        $search = $request->input('search', []);
        $page = $request->get('page', 1);
        $pageNum = $request->get('pageNum', 10);
        $result = new CrmCustomerService()->getCustomerList($page, $pageNum, $search);

        $data = json_decode(json_encode($result), true);

        return view('customer.index', ['data' => $data, 'searchParams' => $search]);
//        return $this->response('', 1, $result);
    }
}
