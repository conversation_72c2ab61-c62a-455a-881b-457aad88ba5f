<?php

namespace App\Http\Controllers\PublicControllers;

use App\Http\Controllers\Controller;
use App\Http\Service\PublicService\CrmSchoolService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CrmSchoolController extends Controller
{
    /**
     * 学校列表
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $params = $request->all();
        $rules = [
            'page' => 'int',
            'pageNum' => 'int',
        ];
        $ruleMessages = [
            'page.int' => 'page is not of int type',
            'pageNum.int' => 'pageNum is not of int type',
        ];
        $valiResult = Validator::make($params, $rules, $ruleMessages);
        if ($valiResult->fails()) {
            return $this->response($valiResult->errors()->first());
        }
        $search = $request->input('search', []);
        $page = $request->get('page', 1);
        $pageNum = $request->get('pageNum', 10);
        $result = new CrmSchoolService()->getSchoolList($page, $pageNum, $search);
        return $this->response('', 1, $result);
    }


    public function show(Request $request, $schoolId): JsonResponse
    {
        return parent::response("123");
    }
}
