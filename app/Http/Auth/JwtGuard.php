<?php


namespace App\Http\Auth;


use App\Http\Service\DepartmentService\UaService;
use Illuminate\Auth\Recaller;
use Illuminate\Auth\SessionGuard;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Cookie;

class JwtGuard extends SessionGuard
{
    /**
     * Queue the recaller cookie into the cookie jar.
     *
     * @param AuthenticatableContract $user
     * @return void
     */
    protected function queueRecallerCookie(AuthenticatableContract $user): void
    {
        //$name, $value, $minutes = 0, $path = null, $domain = null, $secure = null, $httpOnly = true, $raw = false, $sameSite = null
//        $this->getCookieJar()->queue($this->createRecaller(
//            $user->getAuthIdentifier().'|'.$user->getRememberToken().'|'.$user->getAuthPassword()
//        ));
        //修改登陆的cookie 规则
        $this->getCookieJar()->queue($this->createRecaller(optional($user)->api_token));
    }

    /**
     * Get the name of the cookie used to store the "recaller".
     *
     * @return string
     */
    public function getRecallerName(): string
    {
        return env('SSO_COOKIE_KEY', 'ua_token_dev');
    }




    /**
     * Create a "remember me" cookie for a given ID.
     *
     * @param string $value
     * @return Cookie
     */
    protected function createRecaller($value): Cookie
    {
        return $this->getCookieJar()->make($this->getRecallerName(), $value, env('SSO_COOKIE_TIME', 720), '/', env('SSO_COOKIE_DOMAIN', '.thinktown.com'), false, false);
    }

    /**
     * Get the currently authenticated user.
     * 因为sso单点登陆 sso认证不能利用登陆的session来作为另一认证途径 需要对recaller进行额外判断
     *
     * @return AuthenticatableContract|null|void
     */
    public function user()
    {
        if ($this->loggedOut) {
            return null;
        }
        // If we've already retrieved the user for the current request we can just
        // return it back immediately. We do not want to fetch the user data on
        // every call to this method because that would be tremendously slow.
        if (!is_null($this->user)) {
            return $this->user;
        }

        $id = $this->session->get($this->getName());



        // First we will try to load the user using the identifier in the session if
        // one exists.
        // Otherwise, we will check for a "remember me" cookie in this
        // request, and if one exists, attempt to retrieve the user using that.
        if (!is_null($recaller = $this->recaller())) {
            $token = $recaller->id();
            if (false === $this->verifyToken($token)) {
                $this->clearUserDataFromStorage();
                return;
            }
        }
        //与session判断不同对是 如果cookie失效了 那么这个session也就失效
        if (!is_null($id) && !is_null($recaller) && $this->user = $this->provider->retrieveById($id)) {
            $this->fireAuthenticatedEvent($this->user);
        }
        // If the user is null, but we decrypt a "recaller" cookie we can attempt to
        // pull the user data on that cookie which serves as a remember cookie on
        // the application. Once we have a user we can return it to the caller.
        if (is_null($this->user) && !is_null($recaller)) {
            $this->user = $this->userFromRecaller($recaller);

            if ($this->user) {
                $this->updateSession($this->user->getAuthIdentifier());

                $this->fireLoginEvent($this->user, true);
            }
        }

        // If the user is null, but we decrypt a "recaller" cookie we can attempt to
        return $this->user;
    }

    /**
     * Pull a user from the repository by its "remember me" cookie token.
     * 判断方法修改
     *
     * @param Recaller $recaller
     * @return mixed|void
     */
    protected function userFromRecaller($recaller)
    {
        $token = $recaller->id();
        if (false === ($userInfo = $this->verifyToken($token)) || $this->recallAttempted) {
            return;
        }

        // If the user is null, but we decrypt a "recaller" cookie we can attempt to
        // pull the user data on that cookie which serves as a remember cookie on
        // the application. Once we have a user we can return it to the caller.
        $this->recallAttempted = true;

        $this->viaRemember = ! is_null($user = $this->retrieveByToken($this->provider, $userInfo));

        return $user;
    }

    /**
     * @param $provider
     * @param $userInfo
     * @return mixed
     */
    public function retrieveByToken($provider, $userInfo): mixed
    {
        //假设关联model
        $model = $provider->createModel();
        $uaId = Arr::get($userInfo, 'ua_id');

        return $model->newQuery()->where(
            'ua_id', $uaId
        )->first();
    }

    /**
     * Remove the user data from the session and cookies.
     *
     * @return void
     */
    protected function clearUserDataFromStorage(): void
    {
        $this->session->remove($this->getName());
        if (! is_null($this->recaller())) {
            $this->getCookieJar()->queue($this->getCookieJar()
                ->forget($this->getRecallerName(), '/', env('SSO_COOKIE_DOMAIN', 'thinktown.com')));
        }
    }

    /**
     * Get the decrypted recaller cookie for the request.
     *
     * @return Recaller|null|void
     */
    protected function recaller()
    {
        if (is_null($this->request)) {
            return;
        }
        if ($recaller = $this->request->cookies->get($this->getRecallerName())) {
            return new Recaller($recaller);
        }
    }


    protected function verifyToken($token)
    {
        return new UaService()->verifyToken($token, env('SSO_SIGNATURE_KEY'));
    }


    public function attempt(array $credentials = [], $remember = false)
    {
        $this->fireAttemptEvent($credentials, $remember);

        $this->lastAttempted = $user = $this->retrieveByCredentials($credentials);

        // If an implementation of UserInterface was returned, we'll ask the provider
        // to validate the user against the given credentials, and if they are in
        // fact valid we'll log the users into the application and return true.
        if ($user) {
            $this->login($user, true);
            return true;
        }


        // If the authentication attempt fails we will fire an event so that the user
        // may be notified of any suspicious attempts to access their account from
        // an unrecognized user. A developer may listen to this event as needed.
        $this->fireFailedEvent($user, $credentials);

        return false;
    }

    public function login(AuthenticatableContract $user, $remember = false)
    {
        $this->updateSession($user->getAuthIdentifier());

        // If the user should be permanently "remembered" by the application we will
        // queue a permanent cookie that contains the encrypted copy of the user
        // identifier. We will then decrypt this later to retrieve the users.
        $this->queueRecallerCookie($user);

        // If we have an event dispatcher instance set we will fire an event so that
        // any listeners will hook into the authentication events and run actions
        // based on the login and logout events fired from the guard instances.
        $this->fireLoginEvent($user, $remember);

        $this->setUser($user);
    }



    public function retrieveByCredentials(array $credentials) {
        $url = env("UA_URL", "https://ua-kf.thinktown.com:8443");
        $api = "/api/sso/login";
        $data = [
            "username" => $credentials['username'],
            "password" => $credentials['password']
        ];
        $res = Http::post($url . $api, $data);
        $data = json_decode($res->getBody()->getContents(), true);
        if ($data && Arr::has($data, 'data')) {
            $data = $data['data'];
            if ($data) {
                $user = $this->retrieveByToken($this->provider, Arr::pull($data, 'user_info'));
                if ($user) {
                    $user->api_token = Arr::pull($data, 'token');
                    //final
                    return $user;
                }
            }
        }


        return null;
    }
}
