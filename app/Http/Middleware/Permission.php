<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;


/**
 * NOTE 路由权限校验器
 */
class Permission
{
    public function handle($request, Closure $next, ...$guards)
    {
        //进入默认都是登录状态
        $user = Auth::user();
        if ($user) {
            if ($user->is_admin) {
                return $next($request);
            }
            //这是用户的所有权限
            $permission = $user->getAllPermissionsWithMeAndShare();
            $routeCheck = $permission->first(fn ($permission) => $this->shouldPassThroughPermission($request, $permission));
            if ($routeCheck) {
                return $next($request);
            }
        }
        //todo 异常体结构
        $_data = [
            'success' => 1,
            'message' => 'Not have permission',
            'data' =>  new \stdClass(),
            'errorCode' => 1,
        ];
        return new JsonResponse($_data, 200);
    }

    public function shouldPassThroughPermission(Request $request, $permission): bool
    {
        if (empty($permission->http_method) && empty($permission->http_path)) {
            //空配置代表按钮之类的 pass
            return false;
        }
        $method = $permission->http_method;
        $slug = $permission->slug;
        $matches = array_map(function ($path) use ($method, $slug) {
            $path = trim('/api/') . $path;
            $method = explode(',', $method);
            if (Str::contains($path, ':')) {
                list($method, $path) = explode(':', $path);
                $method = explode(',', $method);
            }

            return compact('method', 'path', 'slug');
        }, explode("\n", $permission->http_path));
        return array_any($matches, fn($match) => $this->matchRequest($match, $request) && $this->matchGatesRequest($match['slug'] ?? ''));
    }

    protected function matchGatesRequest($name = ''): bool
    {
        //允许请求
        return Gate::allows($name);
    }

    protected function matchRequest(array $match, Request $request): bool
    {
        if ($match['path'] == '/') {
            $path = '/';
        } else {
            $path = trim($match['path'], '/');
        }
        if (!$request->is($path)) {
            return false;
        }

        $method = collect($match['method'])->filter()->map(function ($method) {
            return strtoupper($method);
        });
        return $method->isEmpty() || $method->contains($request->method());
    }
}
