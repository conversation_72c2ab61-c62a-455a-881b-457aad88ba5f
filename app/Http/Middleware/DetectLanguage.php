<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DetectLanguage
{
    /**
     * Handle an incoming request.
     *
     * @param Closure(Request): (Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $acceptLanguages = ['en', 'zh-CN'];
        $locale = $request->header('Accept-Language');
        if (!in_array($locale, $acceptLanguages)) {
            $locale = 'zh-CN';
        }
        app()->setLocale($locale);
        return $next($request);
    }
}
