<?php

namespace App\Http\Middleware;

use Illuminate\Cookie\Middleware\EncryptCookies as Middleware;

class EncryptCookies extends Middleware
{
    /**
     * The names of the cookies that should not be encrypted.
     *
     * @var array<int, string>
     */
    protected $except = [
        //
    ];

    public function isDisabled($name): bool
    {
        $token = [env('SSO_COOKIE_KEY', '')];
        if (in_array($name, $token)) {
            return true;
        }
        return in_array($name, $this->except);
    }
}
