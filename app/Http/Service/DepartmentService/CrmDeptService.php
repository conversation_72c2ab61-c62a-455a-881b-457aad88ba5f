<?php

namespace App\Http\Service\DepartmentService;

use App\Models\DepartmentModels\CrmDeptModel;
use Illuminate\Support\Facades\DB;

class CrmDeptService
{

    /**
     * 部门树状 num 发生器
     * @return bool
     */
    public function generateDeptNum(): bool
    {
        CrmDeptModel::query()->update(['dept_num' => 0, 'depth' => 0]);// 重置
        $allDepts = CrmDeptModel::all();
        $parentChildMap = [];
        foreach ($allDepts as $dept) {
            $parentChildMap[$dept->parent_id][] = [
                'id' => $dept->id,
                'dept_id' => $dept->dept_id,
                'parent_id' => $dept->parent_id,
            ];
        }
        $resultMap = $this->generateChildNum($parentChildMap, 1, 1001, [], 1);

        $index = 1;
        $end = count($allDepts);
        $ids = [];
        $numCases = "";
        $depthCases = "";
        foreach ($allDepts as $dept) {
            $numCases .= "WHEN id = {$dept->id} THEN '{$resultMap[$dept->id]['num']}'";
            $depthCases .= "WHEN id = {$dept->id} THEN '{$resultMap[$dept->id]['depth']}'";
            $ids[] = $dept->id;
            if ($index % 500 === 0 || $index === $end) {
                $idsStr = implode(',', $ids);
                DB::update("UPDATE crm_dept SET dept_num = CASE {$numCases} END,depth = CASE {$depthCases} END WHERE id IN ({$idsStr})");
                $numCases = "";
                $depthCases = "";
                $ids = [];
            }
            $index++;
        }
        return true;
    }

    /**
     * 递归生成部门 num 和 深度
     * @param array $parentChildMap
     * @param $parentDepartId
     * @param $currentNum
     * @param $resultMap
     * @param $depth
     * @return array
     */
    private function generateChildNum(array $parentChildMap, $parentDepartId, $currentNum, $resultMap, $depth): array
    {
        $targetDept = $parentChildMap[$parentDepartId] ?? [];
        if (empty($targetDept)) {
            return $resultMap;
        }
        $index = 1001;
        foreach ($targetDept as $dept) {
            $num = $currentNum . $index;
            $resultMap[$dept['id']] = [
                'num' => $num,
                'depth' => $depth
            ];
            $resultMap = $this->generateChildNum($parentChildMap, $dept['dept_id'], $num, $resultMap, $depth + 1);
            $index++;
        }
        return $resultMap;
    }

}
