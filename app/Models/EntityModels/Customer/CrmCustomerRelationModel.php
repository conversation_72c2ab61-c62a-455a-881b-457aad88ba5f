<?php

namespace App\Models\EntityModels\Customer;

use App\Models\DepartmentModels\CrmUserModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * 客户用户关系模型
 *
 * 用于管理客户与用户之间的关联关系，包括跟进人、维护人和顾问等角色
 *
 * @property int $id 主键ID
 * @property int $customer_id 客户ID
 * @property int $user_id 用户ID
 * @property Carbon|null $start_time 关系开始时间
 * @property Carbon|null $end_time 关系结束时间
 * @property string|null $memo 备注信息
 * @property int|null $type 关系类型：1-跟进人 2-维护人 3-顾问
 * @property int|null $status 状态：0-无效 1-有效
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 *
 * @property-read CrmCustomerModel $customer 关联的客户
 * @property-read CrmUserModel $user 关联的用户
 *
 * @method static Builder|CrmCustomerRelationModel newModelQuery()
 * @method static Builder|CrmCustomerRelationModel newQuery()
 * @method static Builder|CrmCustomerRelationModel query()
 */
class CrmCustomerRelationModel extends Model
{
    use HasFactory;

    /**
     * 数据库表名
     *
     * @var string
     */
    protected $table = 'customer_relation';

    /**
     * 可批量赋值的字段
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'customer_id',  // 客户ID
        'user_id',      // 用户ID
        'start_time',   // 关系开始时间
        'end_time',     // 关系结束时间
        'memo',         // 备注信息
        'type',         // 关系类型
        'status',       // 状态
        'created_by',   // 创建人ID
        'updated_by',   // 更新人ID
    ];

    /**
     * 类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'customer_id' => 'integer',
        'user_id' => 'integer',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'type' => 'integer',
        'status' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];

    /**
     * 关系类型：跟进人
     */
    public const int TYPE_OWNER = 1;

    /**
     * 关系类型：维护人
     */
    public const int TYPE_SERVER = 2;

    /**
     * 关系类型：顾问
     */
    public const int TYPE_CONSULTANT = 3;


    /**
     * 获取关联的客户
     *
     * 定义与客户模型的一对一关联关系
     *
     * @return BelongsTo
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(CrmCustomerModel::class, 'customer_id');
    }

    /**
     * 获取关联的用户
     *
     * 定义与用户模型的一对一关联关系
     *
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(CrmUserModel::class, 'user_id');
    }
}
