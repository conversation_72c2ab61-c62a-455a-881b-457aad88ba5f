<?php

namespace App\Models\EntityModels\Customer;

use App\Models\EntityModels\CrmEntityModel;
use App\Models\PublicModels\CrmGradeModel;
use App\Models\PublicModels\CrmSchoolModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

/**
 * 客户主表
 *
 * @property int $id vtiger_account.accountid
 * @property string|null $ua_id uaid
 * @property string|null $ua_mobile UA唯一认证电话
 * @property int|null $wx_id 微信识别ID
 * @property string $code 学号,vtiger_account.smpid
 * @property string $name 姓名,vtiger_account.accountname
 * @property string $english_name 英文名
 * @property string|null $is_from_lead 是否由线索转换而来
 * @property int|null $gender 性别,vtiger_account.account_type
 * @property int $customer_stage 客户阶段 1线索 2客户
 * @property string|null $training_status 培训状态,vtiger_account.accountstate
 * @property string|null $study_status 升学状态,vtiger_account.cf_1652
 * @property string|null $source 来源,vtiger_account.rating
 * @property string|null $source_detail 来源详情
 * @property string|null $source_memo 来源备注
 * @property string|null $phone 电话
 * @property string|null $phone_owner 电话归属人
 * @property string|null $other_contact 其他联系方式类型,vtiger_account.contact_1
 * @property string|null $other_contact_detail 其他联系方式详情,vtiger_account.contact_2
 * @property int|null $school 当前学校
 * @property string|null $grade 当前年级
 * @property string|null $school_year 入学年份
 * @property string|null $is_intention 是否意向国际学校
 * @property string|null $course_system 在读课程体系, vtiger_accountcf.cf_1654
 * @property string|null $target_school 目标学校排名
 * @property string|null $abroad_intention 升学意向
 * @property string|null $major_intention 意向专业
 * @property string|null $is_in_xkt 是否新课堂申请
 * @property string|null $other_institution 其他机构
 * @property string|null $apply_year 申请入读年份
 * @property string|null $apply_season 申请季
 * @property string|null $lose_reason 流失原因
 * @property float $total_usd 累计缴纳美元额度
 * @property float $total_rmb 累计缴纳人民币额度
 * @property string|null $last_comm 最新沟通记录
 * @property Carbon|null $next_comm_time 下一次沟通时间
 * @property string|null $description 描述
 * @property int|null $is_deleted 是否已删除 1是 0否
 * @property int $created_by 创建人
 * @property int $updated_by 更新人
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class CrmCustomerModel extends Model implements CrmEntityModel
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'customer';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = true;

    protected $fillable = [
        'ua_id',
        'ua_mobile',
        'wx_id',
        'code',
        'name',
        'english_name',
        'is_from_lead',
        'gender',
        'customer_stage',
        'training_status',
        'study_status',
        'source',
        'source_detail',
        'source_memo',
        'phone',
        'phone_owner',
        'other_contact',
        'other_contact_detail',
        'school',
        'grade',
        'school_year',
        'is_intention',
        'course_system',
        'target_school',
        'abroad_intention',
        'major_intention',
        'is_in_xkt',
        'other_institution',
        'apply_year',
        'apply_season',
        'lose_reason',
        'total_usd',
        'total_rmb',
        'last_comm',
        'next_comm_time',
        'description',
        'is_deleted',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'wx_id' => 'integer',
        'gender' => 'integer',
        'customer_stage' => 'integer',
        'school' => 'integer',
        'total_usd' => 'decimal:2',
        'total_rmb' => 'decimal:2',
        'next_comm_time' => 'datetime',
        'is_deleted' => 'integer',
        'created_by' => 'integer',
        'updated_by' => 'integer',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];


    public function getOwnerId(): int
    {
        $ownerRelation = $this->ownerRelation()->get();
        if (empty($ownerRelation)) {
            return 0;
        }
        return $ownerRelation->user_id;
    }


    /**
     * 获取客户关系
     * @return HasMany
     */
    public function relations(): HasMany
    {
        return $this->hasMany(CrmCustomerRelationModel::class, 'customer_id', 'id');
    }

    /**
     * 获取有效客户关系
     * @return HasMany
     */
    public function activeRelations(): HasMany
    {
        return $this->hasMany(CrmCustomerRelationModel::class, 'customer_id', 'id')
            ->where('status', 1)
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->orderBy('type', 'asc');
    }

    /**
     * 获取跟进人关系
     * @return HasOne
     */
    public function ownerRelation(): HasOne
    {
        return $this->hasOne(CrmCustomerRelationModel::class, 'customer_id', 'id')
            ->where('status', 1)
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->where('type', CrmCustomerRelationModel::TYPE_OWNER);
    }

    /**
     * 获取学员当前学校
     * @return BelongsTo
     */
    public function currentSchool(): BelongsTo
    {
        return $this->belongsTo(CrmSchoolModel::class, 'school', 'id');
    }

    /**
     * 获取学员当前年级
     * @return BelongsTo
     */
    public function currentGrade(): BelongsTo
    {
        return $this->belongsTo(CrmGradeModel::class, 'grade', 'id');
    }


}
