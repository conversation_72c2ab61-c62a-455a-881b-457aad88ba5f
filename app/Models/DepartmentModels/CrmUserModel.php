<?php

namespace App\Models\DepartmentModels;

use App\Http\Service\DepartmentService\CrmUserService;
use App\Traits\HasRoles;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Carbon;


/**
 * 用户模型
 *
 * 管理系统用户信息，包括用户基础信息、部门关联、权限控制等
 *
 * @property int $id 用户ID
 * @property string $name 用户姓名
 * @property string $user_code 用户编号
 * @property int $ua_id UA系统ID
 * @property int $is_admin 是否管理员
 * @property string $position 职位
 * @property int $oa_id OA系统ID
 * @property int $parent_oa_id 上级OA ID
 * @property string $reset_day 重置日期
 * @property int $oa_dept_id OA部门ID
 * @property int $ehr_depart_id EHR部门ID
 * @property int $ehr_uid EHR用户ID
 * @property int $ehr_puid EHR父用户ID
 * @property string $user_num 用户编号(用于层级关系)
 * @property int $status 用户状态(0:离职 1:在职)
 * @property int $campus 校区
 * @property int $region 地区
 * @property int $employ_type 入职类型
 * @property string $staff_mail 邮箱
 * @property string $wx_avatar 微信头像
 * @property int $crm_depart_id CRM部门ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 *
 * @property-read string $formatted_user_name 格式化用户名(姓名 + 工号)
 * @property-read array $departs 负责的部门ID数组
 * @property-read array $all_employees 所有下级员工ID数组
 * @property-read array $shared_privileges 共享权限用户ID数组
 * @property-read array $extra_privileges 额外权限数组
 * @property-read CrmDeptModel $department 所属部门
 * @property-read Collection|CrmDeptModel[] $ownerDepartments 负责的部门
 *
 * @method static \Illuminate\Database\Query\Builder|CrmUserModel newModelQuery()
 * @method static \Illuminate\Database\Query\Builder|CrmUserModel newQuery()
 * @method static \Illuminate\Database\Query\Builder|CrmUserModel query()
 */
class CrmUserModel extends Authenticatable
{
    use Notifiable, HasRoles;

    /**
     * 数据库表名
     *
     * @var string
     */
    protected $table = 'users';

    /**
     * 主键字段名
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 是否自动维护时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 用户状态：在职
     */
    public const int USER_STATUS_ONLINE = 1;

    /**
     * 用户状态：离职
     */
    public const int USER_STATUS_OFFLINE = 0;

    /**
     * 用户状态映射
     */
    public const array USER_STATUS = [
        self::USER_STATUS_ONLINE => '在职',
        self::USER_STATUS_OFFLINE => '离职',
    ];

    /**
     * 可批量赋值的属性
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'is_admin',      // 是否管理员
        'ua_id',         // UA系统ID
        'user_code',     // 用户编号
        'name',          // 用户姓名
        'position',      // 职位
        'oa_id',         // OA系统ID
        'parent_oa_id',  // 上级OA ID
        'reset_day',     // 重置日期
        'oa_dept_id',    // OA部门ID
        'ehr_depart_id', // EHR部门ID
        'ehr_uid',       // EHR用户ID
        'ehr_puid',      // EHR父用户ID
        'user_num',      // 用户编号(用于层级关系)
        'status',        // 用户状态
        'campus',        // 校区
        'region',        // 区域
        'employ_type',   // 雇佣类型
        'wx_avatar',     // 微信头像
        'crm_depart_id', // CRM部门ID
    ];

    /**
     * 属性类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_admin' => 'integer',      // 是否管理员
        'oa_id' => 'integer',         // OA系统ID
        'parent_oa_id' => 'integer',  // 上级OA ID
        'oa_dept_id' => 'integer',    // OA部门ID
        'ehr_depart_id' => 'integer', // EHR部门ID
        'ehr_uid' => 'integer',       // EHR用户ID
        'ehr_puid' => 'integer',      // EHR父用户ID
        'status' => 'integer',        // 用户状态
        'campus' => 'integer',        // 校区
        'region' => 'integer',        // 区域
        'employ_type' => 'integer',   // 雇佣类型
        'crm_depart_id' => 'integer', // CRM部门ID
    ];


    /**
     * 获取格式化后的用户名
     *
     * 格式：姓名 (工号)
     *
     * @return string 格式化后的用户名
     */
    public function getFormattedUserNameAttribute(): string
    {
        return $this->name . ' (' . $this->user_code . ')';
    }


    /**
     * 获取用户负责的部门
     *
     * 获取当前用户作为管理员的部门列表
     * 只返回未删除的部门
     *
     * @return HasMany 部门关联关系
     */
    public function ownerDepartments(): HasMany
    {
        return $this->hasMany(CrmDeptModel::class, 'manage_id', 'id')->where('is_deleted', 0);
    }


    /**
     * 获取用户所属部门
     *
     * 获取用户当前所属的部门信息
     *
     * @return BelongsTo 部门关联关系
     */
    public function department(): BelongsTo
    {
        return $this->belongsTo(CrmDeptModel::class, 'crm_depart_id', 'id');
    }


    /**
     * 获取用户树中所有下级员工及自己
     *
     * 根据user_num字段的层级关系查询所有下级员工
     *
     * @return Collection|static[] 用户集合
     */
    public function employees(): Collection|static
    {
        return self::where('user_num', 'like', $this->user_num . '%')->get();
    }


    /**
     * 获取部门树中自己负责的部门及其下属部门
     *
     * 返回部门ID数组，包含当前用户负责的部门及其所有子部门
     * 结果会被缓存到模型属性中，避免重复查询
     *
     * @return array<int> 部门ID数组
     */
    public function getDepartsAttribute(): array
    {
        if (isset($this->attributes['departs'])) {
            return $this->attributes['departs'];
        }
        $departs = $this->ownerDepartments()->get();
        $result = [];
        foreach ($departs as $depart) {
            $result[$depart->id] = $depart;
            $children = $depart->children();
            foreach ($children as $child) {
                $result[$child->id] = $child;
            }
        }
        $this->attributes['departs'] = array_keys($result);
        return $result;
    }


    /**
     * 获取所有下级员工ID，包含用户树及部门树中所有下级
     *
     * 1. 获取用户树中的所有下级（基于user_num的层级关系）
     * 2. 获取负责部门树中的所有员工
     * 结果会被缓存到模型属性中，避免重复查询
     *
     * @return array<int> 用户ID数组
     */
    public function getAllEmployeesAttribute(): array
    {
        if (isset($this->attributes['all_employees'])) {
            return $this->attributes['all_employees'];
        }
        // 先获取人员树中的所有下级
        $employees = $this->employees();
        $result = [];
        foreach ($employees as $employee) {
            $result[$employee->id] = $employee;
        }

        // 再获取部门树中的所有下级员工
        $departIds = $this->departs;
        $users = CrmUserModel::whereIn('crm_depart_id', $departIds)->get();
        foreach ($users as $user) {
            $result[$user->id] = $user;
        }

        $this->attributes['all_employees'] = array_keys($result);
        return $result;
    }

    /**
     * 初始化用户业务数据
     *
     * 预加载用户相关的业务数据，包括：
     * 1. 负责的部门
     * 2. 所有下级员工
     * 3. 额外权限
     * 4. 共享权限
     *
     * @return $this 当前模型实例，支持链式调用
     */
    public function initUserBusinessParams(): static
    {
        $this->getDepartsAttribute();
        $this->getAllEmployeesAttribute();
        $this->getUserExtraPrivilegesAttribute();
        $this->getUserSharePrivilegesAttribute();
        return $this;
    }


    /**
     * 获取用户权限分享数据
     *
     * @return array[]
     */
    public function getUserSharePrivilegesAttribute(): array
    {
        if (isset($this->attributes['shared_privileges'])) return $this->attributes['shared_privileges'];

        $sharedUsers = [];
        $userId = $this->id;
        $departId = $this->crm_depart_id;
        $extraPrivileges = $this->extra_privileges;

        $privileges = CrmSharePrivilegeModel::where('status', CrmSharePrivilegeModel::STATUS_ENABLE)
            ->where(function (Builder $query) use ($userId, $departId) {
                $query->where(function (Builder $query) use ($userId) {
                    // 1人-人
                    // 2人及下属-人
                    // 3部门-人
                    // 4部门及下属部门-人
                    $query->whereJsonContains('to_ids', $userId)
                        ->whereIn('obj_type', [
                            CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON
                        ]);
                })
                    ->orWhere(function (Builder $query) use ($departId) {
                        // 5人-部门
                        // 6人及下属-部门
                        // 7部门-部门
                        // 8部门及下属部门-部门
                        $query->whereJsonContains('to_ids', $departId)
                            ->whereIn('obj_type', [
                                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT,
                            ]);
                    });
            })
            ->where('share_type', CrmSharePrivilegeModel::SHARE_TYPE_SHARE)
            ->where('start_date', '<=', date('Y-m-d'))
            ->where('end_date', '>=', date('Y-m-d'))
            ->get();

        foreach ($privileges as $item) {
            if (in_array($item->obj_type, [
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
            ])) {
                // 授权源头是人
                $targetUsers = CrmUserModel::whereIn('id', $item->from_ids)->get();
                foreach ($targetUsers as $targetUser) {
                    if ($targetUser->id == $userId) {
                        continue;
                    }
                    $sharedUsers[$targetUser->id] = $targetUser;
                    if ($item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON ||
                        $item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT) {
                        // 附带人员树下级
                        $allEmployees = $targetUser->employees() ?? [];
                        foreach ($allEmployees as $employee) {
                            if ($employee->id == $targetUser->id || $employee->id == $userId) {
                                continue;
                            }
                            $sharedUsers[$employee->id] = $employee;
                        }
                    }
                }
            } else {
                // 授权源头是部门
                $targetDepts = CrmDeptModel::whereIn('id', $item->from_ids)->get();
                foreach ($targetDepts as $targetDept) {
                    $thisTargetUsers = $targetDept->menbers;
                    foreach ($thisTargetUsers as $targetUser) {
                        if ($targetUser->id == $userId) {
                            continue;
                        }
                        $sharedUsers[$targetUser->id] = $targetUser;
                    }
                    if ($item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON ||
                        $item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT) {
                        // 附带子部门
                        $childrenDepts = $targetDept->children();
                        foreach ($childrenDepts as $childrenDept) {
                            $thisChildrenUsers = $childrenDept->menbers;
                            foreach ($thisChildrenUsers as $childrenUser) {
                                if ($childrenUser->id == $userId) {
                                    continue;
                                }
                                $sharedUsers[$childrenUser->id] = $childrenUser;
                            }
                        }
                    }
                }
            }
        }
        $usersExtraPrivileges = new CrmUserService()->getBatchExtraPrivileges($sharedUsers);
        foreach ($usersExtraPrivileges as $role => $privileges) {
            $extraPrivileges[$role] = array_unique(array_merge($extraPrivileges[$role] ?? [], $privileges));
        }

        $this->attributes['shared_privileges'] = array_keys($sharedUsers);
        $this->attributes['extra_privileges'] = $extraPrivileges;
        return $sharedUsers;
    }


    /**
     * 额外数据权限
     * @return array
     */
    public function getUserExtraPrivilegesAttribute(): array
    {
        if (isset($this->attributes['extra_privileges'])) return $this->attributes['extra_privileges'];

        $userId = $this->id;
        $departId = $this->crm_depart_id;
        $owners = [];
        $servers = [];
        $consultants = [];

        $privileges = CrmSharePrivilegeModel::where('status', CrmSharePrivilegeModel::STATUS_ENABLE)
            ->where(function (Builder $query) use ($userId, $departId) {
                $query->where(function (Builder $query) use ($userId) {
                    // 1人-人
                    // 2人及下属-人
                    // 3部门-人
                    // 4部门及下属部门-人
                    $query->whereJsonContains('to_ids', $userId)
                        ->whereIn('obj_type', [
                            CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_PERSON,
                            CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON
                        ]);
                })
                    ->orWhere(function (Builder $query) use ($departId) {
                        // 5人-部门
                        // 6人及下属-部门
                        // 7部门-部门
                        // 8部门及下属部门-部门
                        $query->whereJsonContains('to_ids', $departId)
                            ->whereIn('obj_type', [
                                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT,
                                CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT,
                            ]);
                    });
            })
            ->where('share_type', CrmSharePrivilegeModel::SHARE_TYPE_EXTRA)
            ->where('start_date', '<=', date('Y-m-d'))
            ->where('end_date', '>=', date('Y-m-d'))
            ->get();

        foreach ($privileges as $item) {
            $roles = $item->roles;
            if (in_array($item->obj_type, [
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_PERSON,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_TO_DEPARTMENT,
                CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT,
            ])) {
                // 授权源头是人
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $item->from_ids);
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $item->from_ids);
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $item->from_ids);
                if ($item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON || $item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT) {
                    // 只获取用户编号
                    $userNums = CrmUserModel::whereIn('id', $item->from_ids)->pluck('user_num');
                    // 只有在找到了 user_num 时才需要查询下属
                    if ($userNums->isNotEmpty()) {
                        // 使用 where closure 构建查询，避免多个 orWhere 导致的性能问题，并只获取ID
                        $subordinateIds = CrmUserModel::where(function ($query) use ($userNums) {
                            foreach ($userNums as $num) {
                                $query->orWhere('user_num', 'like', $num . '%');
                            }
                        })->pluck('id'); // 直接获取所有符合条件的下属ID集合
                        if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $subordinateIds->toArray());
                        if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $subordinateIds->toArray());
                        if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $subordinateIds->toArray());
                    }
                }
            } else {
                // 授权源头是部门
                $targetUsers = CrmUserModel::whereIn('crm_depart_id', $item->from_ids)->get();
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, array_keys($targetUsers));
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, array_keys($targetUsers));
                if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, array_keys($targetUsers));
                if ($item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON ||
                    $item->obj_type == CrmSharePrivilegeModel::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT) {
                    // 附带子部门
                    $deptNums = CrmDeptModel::whereIn('id', $item->from_ids)->pluck('dept_num');
                    if ($deptNums->isNotEmpty()) {
                        $subDeptIds = CrmDeptModel::where(function ($query) use ($deptNums) {
                            foreach ($deptNums as $num) {
                                $query->orWhere('dept_num', 'like', $num . '%');
                            }
                        })->pluck('id');
                        if ($subDeptIds->isNotEmpty()) {
                            $deptUsers = CrmUserModel::whereIn('crm_depart_id', $subDeptIds->toArray())->pluck('id');
                            if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_OWNER, $roles)) $owners = array_merge($owners, $deptUsers->toArray());
                            if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_SERVER, $roles)) $servers = array_merge($servers, $deptUsers->toArray());
                            if (in_array(CrmSharePrivilegeModel::SHARE_ROLE_CONSULTANT, $roles)) $consultants = array_merge($consultants, $deptUsers->toArray());
                        }
                    }
                }
            }
        }

        $this->attributes['extra_privileges'] = [
            'owners' => array_keys($owners),
            'servers' => array_keys($servers),
            'consultants' => array_keys($consultants),
        ];
        return [
            'owners' => array_keys($owners),
            'servers' => array_keys($servers),
            'consultants' => array_keys($consultants),
        ];
    }

    /**
     * A user has and belongs to many roles.
     * 后台角色
     * @return BelongsToMany
     */
    public function backendRoles(): BelongsToMany
    {
        $pivotTable = config('admin.database.role_users_table');

        $relatedModel = config('admin.database.roles_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'role_id');
    }

    /**
     * A User has and belongs to many permissions.
     * 后台权限
     * @return BelongsToMany
     */
    public function backendPermissions(): BelongsToMany
    {
        $pivotTable = config('admin.database.user_permissions_table');

        $relatedModel = config('admin.database.permissions_model');

        return $this->belongsToMany($relatedModel, $pivotTable, 'user_id', 'permission_id');
    }

}
