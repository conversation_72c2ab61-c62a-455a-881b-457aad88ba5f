<?php

namespace App\Models\DepartmentModels;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;

/**
 * Class CrmSharePrivilegeModel
 *
 * 共享权限模型
 *
 * @property int $id 主键ID
 * @property int $share_type 共享类型：0-额外数据权限，1-权限分享
 * @property int $obj_type 对象类型：1-人-人，2-人及下属-人，3-部门-人，4-部门及下属部门-人，5-人-部门，6-人及下属-部门，7-部门-部门，8-部门及下属部门-部门
 * @property string $from_ids 来源ID集合，JSON格式
 * @property int $to_id 目标ID
 * @property Carbon $start_date 有效开始日期
 * @property Carbon $end_date 有效结束日期
 * @property int $status 状态：0-禁用，1-启用
 * @property string $roles 权限业务角色 1跟进人2维护人3教育顾问
 * @property string $ext 额外配置
 * @property string $memo 备注
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 */
class CrmSharePrivilegeModel extends Model
{
    /**
     * 对象类型：人-人
     */
    public const int OBJ_TYPE_PERSON_TO_PERSON = 1;

    /**
     * 对象类型：人及下属-人
     */
    public const int OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON = 2;

    /**
     * 对象类型：部门-人
     */
    public const int OBJ_TYPE_DEPARTMENT_TO_PERSON = 3;

    /**
     * 对象类型：部门及下属部门-人
     */
    public const int OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON = 4;

    /**
     * 对象类型：人-部门
     */
    public const int OBJ_TYPE_PERSON_TO_DEPARTMENT = 5;

    /**
     * 对象类型：人及下属-部门
     */
    public const int OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT = 6;

    /**
     * 对象类型：部门-部门
     */
    public const int OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT = 7;

    /**
     * 对象类型：部门及下属部门-部门
     */
    public const int OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT = 8;

    /**
     * 对象类型描述映射
     *
     * @var array<int, string>
     */
    public static array $objTypeDescriptions = [
        self::OBJ_TYPE_PERSON_TO_PERSON => '人-人',
        self::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_PERSON => '人及下属-人',
        self::OBJ_TYPE_DEPARTMENT_TO_PERSON => '部门-人',
        self::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_PERSON => '部门及下属部门-人',
        self::OBJ_TYPE_PERSON_TO_DEPARTMENT => '人-部门',
        self::OBJ_TYPE_PERSON_WITH_SUBORDINATES_TO_DEPARTMENT => '人及下属-部门',
        self::OBJ_TYPE_DEPARTMENT_TO_DEPARTMENT => '部门-部门',
        self::OBJ_TYPE_DEPARTMENT_WITH_SUB_TO_DEPARTMENT => '部门及下属部门-部门',
    ];

    /**
     * 状态：启用
     */
    public const int STATUS_ENABLE = 1;

    /**
     * 状态：禁用
     */
    public const int STATUS_DISABLE = 0;

    /**
     * 状态描述映射
     *
     * @var array<int, string>
     */
    public static array $statusDescriptions = [
        self::STATUS_ENABLE => '启用',
        self::STATUS_DISABLE => '禁用',
    ];

    /**
     * 共享类型：额外数据权限
     */
    public const int SHARE_TYPE_EXTRA = 0;

    /**
     * 共享类型：权限分享
     */
    public const int SHARE_TYPE_SHARE = 1;

    /**
     * 共享类型描述映射
     *
     * @var array<int, string>
     */
    public static array $shareTypeDescriptions = [
        self::SHARE_TYPE_EXTRA => '额外数据权限',
        self::SHARE_TYPE_SHARE => '权限分享',
    ];

    /**
     * 角色：跟进人
     */
    public const int SHARE_ROLE_OWNER = 1;

    /**
     * 角色：维护人
     */
    public const int SHARE_ROLE_SERVER = 2;

    /**
     * 角色：教育顾问
     */
    public const int SHARE_ROLE_CONSULTANT = 3;

    /**
     * 角色描述映射
     *
     * @var array<int, string>
     */
    public static array $shareRoleDescriptions = [
        self::SHARE_ROLE_OWNER => '跟进人',
        self::SHARE_ROLE_SERVER => '维护人',
        self::SHARE_ROLE_CONSULTANT => '教育顾问',
    ];

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'share_privilege';

    /**
     * 主键名
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 是否自增
     *
     * @var bool
     */
    public $incrementing = true;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'share_type',
        'from_ids',
        'to_ids',
        'obj_type',
        'start_date',
        'end_date',
        'status',
        'memo',
        'created_by',
        'updated_by',
        'ext',
        'roles',
    ];


    protected $casts = [
        'from_ids' => 'json',
        'to_ids' => 'json',
        'ext' => 'json',
        'roles' => 'json',
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];


    /**
     * Set the value of the "from_ids" attribute.
     * @param $value
     * @return void
     */
    public function setFromIdsAttribute($value): void
    {
        $this->attributes['from_ids'] = json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK);
    }

    /**
     * Set the value of the "to_ids" attribute.
     * @param $value
     * @return void
     */
    public function setToIdsAttribute($value): void
    {
        $this->attributes['to_ids'] = json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_NUMERIC_CHECK);
    }
}
