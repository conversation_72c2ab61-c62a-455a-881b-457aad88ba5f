<?php

namespace App\Models\FastQueryModel;

use App\Traits\FastModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 通用快速查询模型
 */
class FastQueryModel extends Model
{
    use FastModel;

    public static function permissions(): Model
    {
        return self::tableModel("permissions");
    }

    public static function permissionsQ(): Builder {
        return self::table("permissions");
    }

    public static function roleHasPermissionsQ(): Builder
    {
        return self::table("role_has_permissions");
    }

    public static function roleHasPermissions(): Model
    {
        return self::tableModel("role_has_permissions");
    }

    public function permissionHasRoleR(): HasMany
    {
        return $this->hasMany("roleHasPermissions", "permission_id", "id");
    }
    public function permissionHasRoleR2(): HasMany
    {
        return $this->hasMany("roleHasPermissionsQ", "permission_id", "id");
    }
}
