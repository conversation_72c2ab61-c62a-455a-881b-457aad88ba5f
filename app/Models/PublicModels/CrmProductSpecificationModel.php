<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

/**
 * Class CrmProductSpecificationModel
 *
 * 产品规格模型
 *
 * @property int $id 主键ID
 * @property int $category_id 产品类别ID
 * @property string $code 产品编码
 * @property string $name_cn 产品中文名称
 * @property string|null $name_en 产品英文名称
 * @property string|null $memo 备注
 * @property float|null $exceeding 计划类产品是否允许超额 0否 1是
 * @property string $currency 货币类型
 * @property int $status 状态：0-禁用，1-启用
 * @property int|null $level_id 业务分级ID
 * @property int|null $achievement_id 绩效类型ID
 * @property int $custom_price 是否自定义价格：0-否，1-是
 * @property int $price_type 价格类型 0单价 1总价
 * @property float|null $static_price 总价模式下的固定数量
 * @property int $created_by 创建人ID
 * @property int $updated_by 更新人ID
 * @property Carbon $created_at 创建时间
 * @property Carbon $updated_at 更新时间
 *
 * @property-read CrmProductCategoryModel $category 关联的产品类别
 * @property-read CrmProductLevelModel|null $level 关联的业务分级
 * @property-read CrmAchievementModel|null $achievement 关联的绩效类型
 * @property-read Collection|CrmProductSpecificationPriceModel[] $prices 关联的产品价格列表
 * @property-read Collection|CrmProductSpecificationSubjectsModel[] $subjects 关联的适用科目
 * @property-read CrmProductSpecificationPriceModel|null $currentPrice 关联的当前有效价格
 * @property-read CrmProductSpecificationFactorsModel|null $region 关联的适用地区
 * @property-read CrmProductSpecificationFactorsModel|null $classType 关联的适用班型
 * @property-read CrmProductSpecificationFactorsModel|null $teacherType 关联的教师类型
 * @property-read CrmProductSpecificationFactorsModel|null $pricingType 关联的定价类型
 */
class CrmProductSpecificationModel extends Model
{
    use HasFactory;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'product_specification';

    /**
     * 主键名
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'id',
        'category_id',
        'code',
        'name_cn',
        'name_en',
        'memo',
        'exceeding',
        'currency',
        'status',
        'level_id',
        'achievement_id',
        'custom_price',
        'price_type',
        'static_price',
        'created_by',
        'updated_by',
        'created_at',
        'updated_at'
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];


    /**
     * 获取产品类别
     * @return BelongsTo
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(CrmProductCategoryModel::class, 'category_id', 'id');
    }


    /**
     * 获取产品业务分级
     * @return BelongsTo
     */
    public function level(): BelongsTo
    {
        return $this->belongsTo(CrmProductLevelModel::class, 'level_id', 'id');
    }


    /**
     * 获取产品绩效类型
     * @return BelongsTo
     */
    public function achievement(): BelongsTo
    {
        return $this->belongsTo(CrmAchievementModel::class, 'achievement_id', 'id');
    }


    /**
     * 获取产品价格
     * @return HasMany
     */
    public function prices(): HasMany
    {
        return $this->hasMany(CrmProductSpecificationPriceModel::class, 'specification_id', 'id');
    }


    /**
     * 获取产品适用科目
     * @return HasMany
     */
    public function subjects(): HasMany
    {
        return $this->hasMany(CrmProductSpecificationSubjectsModel::class, 'specification_id', 'id')->with('subject');
    }


    /**
     * 获取产品当前价格
     * @return HasOne
     */
    public function currentPrice(): HasOne
    {
        return $this->hasOne(CrmProductSpecificationPriceModel::class, 'specification_id', 'id')->where('status', 1)->where('start_date', '<=', date('Y-m-d'))->where('end_date', '>=', date('Y-m-d'));
    }


    /**
     * 获取产品适用地区
     * @return HasOne
     */
    public function region(): HasOne
    {
        return $this->hasOne(CrmProductSpecificationFactorsModel::class, 'specification_id', 'id')->where('pricing_factor_id', CrmPricingFactorValueModel::REGION);
    }


    /**
     * 获取产品适用班型
     * @return HasOne|null
     */
    public function classType(): ?HasOne
    {
        return $this->hasOne(CrmProductSpecificationFactorsModel::class, 'specification_id', 'id')->where('pricing_factor_id', CrmPricingFactorValueModel::CLASS_TYPE);
    }


    /**
     * 获取产品适用教师类型
     * @return HasOne
     */
    public function teacherType(): HasOne
    {
        return $this->hasOne(CrmProductSpecificationFactorsModel::class, 'specification_id', 'id')->where('pricing_factor_id', CrmPricingFactorValueModel::TEACHER_TYPE);
    }


    /**
     * 获取产品定价类型
     * @return HasOne
     */
    public function pricingType(): HasOne
    {
        return $this->hasOne(CrmProductSpecificationFactorsModel::class, 'specification_id', 'id')->where('pricing_factor_id', CrmPricingFactorValueModel::PRICING_TYPE);
    }
}
