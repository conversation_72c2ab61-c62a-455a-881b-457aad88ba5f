<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CrmProductSpecificationPriceModel extends Model
{
    use HasFactory;

    protected $table = 'product_specification_price';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'specification_id',
        'price',
        'start_date',
        'end_date',
        'status',
        'created_by',
        'created_at',
    ];

    /**
     * 删除动作为改状态
     * @return true
     */
    public function delete(): true
    {
        $this->status = 0;
        $this->save();
        return true;
    }

}
