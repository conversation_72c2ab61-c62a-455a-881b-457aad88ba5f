<?php

namespace App\Models\PublicModels;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CrmPricingFactorValueModel extends Model
{
    use HasFactory;

    protected $table = 'pricing_factor_value';
    protected $primaryKey = 'id';
    public $timestamps = false;


    const int REGION = 1;
    const int CLASS_TYPE = 2;
    const int TEACHER_TYPE = 3;
    const int PRICING_TYPE = 4;

    const array factorDescriptionCn = [
        self::REGION => '地区',
        self::CLASS_TYPE => '班型',
        self::TEACHER_TYPE => '教师类型',
        self::PRICING_TYPE => '定价方案',
    ];

    const array factorDescriptionEn = [
        self::REGION => 'Region',
        self::CLASS_TYPE => 'ClassType',
        self::TEACHER_TYPE => 'TeacherType',
        self::PRICING_TYPE => 'PricingType',
    ];

    /**
     * 获取地区list
     * @return array
     */
    public static function regionList(): array
    {
        return self::where('pricing_factor_id', self::REGION)->get()->toArray();
    }

    /**
     * 获取班型list
     * @return array
     */
    public static function classTypeList(): array
    {
        return self::where('pricing_factor_id', self::CLASS_TYPE)->get()->toArray();
    }

    /**
     * 获取教师类型list
     * @return array
     */
    public static function teacherTypeList(): array
    {
        return self::where('pricing_factor_id', self::TEACHER_TYPE)->get()->toArray();
    }

    /**
     * 获取定价类型list
     * @return array
     */
    public static function pricingTypeList(): array
    {
        return self::where('pricing_factor_id', self::PRICING_TYPE)->get()->toArray();
    }


}
