<?php

namespace App\Utils;

use Exception;
use Illuminate\Support\Facades\Http;
use phpseclib3\Crypt\PublicKeyLoader;
use phpseclib3\Crypt\RSA;
use phpseclib3\Crypt\RSA\PublicKey;

/**
 * OA相关工具
 */
class OAUtil
{
    /**
     * OA认证三部曲：1.注册 2. 获取token 3.拿token请求
     */

    //注册用rsa
    public static RSA|null $rsa = null;

    //认证token
    public static string|null $token = null;

    //注册密钥
    public static string|null $secret = null;

    //OA公钥
    public static PublicKey|null $spk = null;


    const string CREATE_WORK_FLOW = "/";

    const string GET_WORK_FLOW = "/";


    /**
     * 注册
     * @return void
     * @throws Exception
     */
    public static function register()
    {
        //响应体：{"msg":"ok","code":0,"msgShowType":"none","secrit":"4137c063-659b-48ac-9573-dc5a48d2841a","secret":"4137c063-659b-48ac-9573-dc5a48d2841a","status":true,"spk":"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoBAy/obrU8Pw2MK8X6qYW6Au9xC4XSbWG8+XmcoqZa7nCkYxRCOmwrGqFmLsSZx6zR2XGDD22DI2fF5j+jyQt6jXfcq50/jyyEegoXaA3MMcH5Y1Mmel7/Gnx9hwnKV44OD/TuY0JesRbm1yI7veZ2IKkF6UZh6E8CodLupERlKVeG++zL0rxvB7Q0gqiRgiX/VQDR/X9smeDXrFnD6lo3EKqfSzeGogNKBmoSbcBmuJdfNVtl5cguedgDmfmwKGZ6M/mMTiC0RBVlatQK7LBY1A+fNzuHCAdFmGPYmmctbYmO0HYH5myx2szVrdnIuOiB/z55hCvMdKVzOTmESMzwIDAQAB"}
        $rsa = self::OARsa();
        $url = env("OA_URL", "https://oa-kf.thinktown.com");
        $api = "/api/ec/dev/auth/regist";
        $appid = env("OA_APPID", "881ed174-a092-4248-809c-2d4ebd02907b");
        $doReq = Http::withHeaders(["appid" => $appid, "cpk" => $rsa->getPublicKey()->getFingerprint("sha256"),])->post($url . $api);
        if ($doReq->ok() && $doReq["code"] === 0) {
            self::$secret = $doReq["secret"];
            self::$spk = PublicKeyLoader::loadPublicKey($doReq["spk"]);
        } else {
            throw new Exception("OA 注册失败");
        }
    }

    /**
     * @throws Exception
     */
    public static function token()
    {
        if (self::$token != null) {
            return self::$token;
        }
        if (!self::$spk || !self::$secret) {
            self::register();
        }
        $url = env("OA_URL", "https://oa-kf.thinktown.com");
        $api = "/api/ec/dev/auth/applytoken";
        $appid = env("OA_APPID", "881ed174-a092-4248-809c-2d4ebd02907b");
        $tokenTime = env("OA_TOKEN_TIME", 3600);
        $doReq = Http::withHeaders([
            "appid" => $appid,
            "secret" => self::encrypt(self::$secret),
            "time" => $tokenTime,
        ])->post($url . $api);
        if ($doReq->ok() && $doReq["code"] === 0) {
            self::$token = $doReq['token'];
            return self::$token;
        } else {
            throw new Exception("获取token失败");
        }
    }

    /**
     * 发起OA请求
     * @param $api | 目标地址
     * @param $oaId | 用户的oaId
     * @param string $method | 请求方式
     * @param array $data | 数据体
     * @return mixed
     * @throws Exception
     */
    public static function doOARequest($api, $oaId, array $data = [], string $method = "POST"): mixed
    {
        $url = env("OA_URL", "https://oa-kf.thinktown.com") . $api;
        $pending = Http::withHeaders(self::requestHeader($oaId));
        if ($method == "POST") {
            //只允许form提交
            $response = $pending->asForm()->post($url, $data);
        } else {
            $response = $pending->withQueryParameters($data)->get($url);
        }
        //需要注意返回体里不一定都是json 要调用对应接口时判断
        return $response->body();
    }

    public static function OARsa(): ?RSA\PrivateKey
    {
        if (self::$rsa == null) {
            self::$rsa = RSA::createKey();
        }
        return self::$rsa;
    }

    /**
     * 加密数据
     * @param string $data
     * @return string
     * @throws Exception
     */
    public static function encrypt(string $data): string
    {
        if (!self::$spk) {
            self::register();
        }
        return base64_encode(self::$spk->withPadding(RSA::ENCRYPTION_PKCS1 | RSA::SIGNATURE_PKCS1)->encrypt($data));
    }

    /**
     * 创建新流程
     * @return int Request ID
     */
    public static function createWorkFlow($oaId, $workFlowId, $requestName, $mainData, $detailData = "", $requestLevel = "", $remark = "", $otherParams = ""): int
    {
         $api = "/api/workflow/paService/doCreateRequest";
         $data = [
             "workflowId" => $workFlowId,
             "requestName" => $requestName,
             "mainData" => json_encode($mainData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
//             "detailData" => $detailData,
//             "requestLevel" => $requestLevel,
//             "remark" => $remark,
//             "otherParams" => $otherParams,
         ];
         $r = self::doOARequest($api, $oaId, $data);
         dd($r);
        return 0;
    }

    /**
     * @throws Exception
     */
    public static function requestHeader($oaId): array
    {
        $token = self::token();
        return [
            "appid" => env("OA_APPID", "881ed174-a092-4248-809c-2d4ebd02907b"),
            "token" => $token,
            "userid" => self::encrypt($oaId),
        ];
    }

    /**
     * 获取流程详情
     * @return mixed
     */
    public static function getWorkFlow($requestId): null
    {
        return null;
    }


}
