<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;

/**
 * 关系的fast model 版本, 传入的类请改写成对应的静态方法即可
 */
trait HasRelationshipsExtension
{
    /**
     * Create a new model instance for a related model.
     *
     * @param string $class
     * @return mixed
     */
    protected function newRelatedInstance($class): mixed
    {
        //静态调用本地
        $instance = self::$class();

        if (is_a($instance, Builder::class)) {
            $instance = $instance->getModel();
        }

        if (!$instance->getConnectionName()) {
            $instance->setConnection($this->connection);
        }

        return $instance;
    }

    /**
     * Create a new model instance for a related "through" model.
     *
     * @param string $class
     * @return mixed
     */
    protected function newRelatedThroughInstance($class): mixed
    {
        $instance = self::$class();
        if (is_a($instance, Builder::class)) {
            $instance = $instance->getModel();
        }
        return $instance;
    }


}
