<?php

namespace App\Traits;

use Illuminate\Support\Collection;
use Spatie\Permission\Contracts\Permission;
use Spatie\Permission\Contracts\Role;
use Spatie\Permission\Traits\HasPermissions as BaseHasPermissions;

trait HasPermissions
{
    use BaseHasPermissions;

    public function getAllPermissionsWithMeAndShare(): Collection
    {
        //1. 本人的所有权限
        $permissions = $this->getAllPermissions();

        if (! is_a($this, Permission::class)) {
            $permissions = $permissions->merge($this->getPermissionsWithShare())->merge($this->getPermissionsWithShareViaRoles());
        }
        return $permissions->sort()->values();
    }

    public function getPermissionsWithShare(): Collection
    {
        //
        if ($this->shared_privileges) {
            //todo 链接缓存
            return $this->newQuery()
                ->select(["id"])->whereIn("id", $this->shared_privileges)
                ->with("permissions")
                ->get()
                ->flatMap(fn($user) => $user->permissions)
                ->sort()
                ->values();
        }

        return collect();
    }

    public function getPermissionsWithShareViaRoles(): Collection
    {
        //模型调用才通过
        if (is_a($this, Role::class) || is_a($this, Permission::class)) {
            return collect();
        }
        if ($this->shared_privileges) {
            return $this->newQuery()
                ->select(["id"])->whereIn("id", $this->shared_privileges)
                ->with("roles", "roles.permissions")
                ->get()
                ->flatMap(function ($user) {
                    return collect($user->roles)->flatMap(fn($role) => $role->permissions);
                })
                ->sort()
                ->values();
        }

        return collect();
    }
}
