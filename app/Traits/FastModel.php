<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

trait FastModel
{
    use HasRelationshipsExtension;

    /**
     * 返回的是builder builder可以获取model，简单的快速查询器
     * @param $table
     * @return Builder
     */
    public static function table($table): Builder
    {
        return (new static)->setTable($table)->newQuery();
    }

    /**
     * 返回的是fast model，可以用来定义字段、cast等、方便一些快速查询
     * @param $table
     * @return Model
     */
    public static function tableModel($table): Model
    {
        return (new static)->setTable($table);
    }

}
