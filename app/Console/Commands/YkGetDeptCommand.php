<?php

namespace App\Console\Commands;

use App\Http\Service\DepartmentService\CrmDeptService;
use App\Http\Service\DepartmentService\YkService;
use App\Models\DepartmentModels\CrmDeptModel;
use App\Models\DepartmentModels\CrmUserModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class YkGetDeptCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:yk-get-dept {--initialize : 是否初始化部门数据}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新部门信息';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $topDbName = env('TOP_DB_NAME', 'top');
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');

        if ($this->option('initialize')) {
            $this->info(date("Y-m-d H:i:s") . ' 开始初始化部门数据');
            DB::statement("TRUNCATE TABLE crm_dept");
            //写入team数据
            DB::statement("INSERT INTO crm_dept (id,dept_id,dept_name,parent_id,oa_dept_id,ehr_id,ehr_uid,manage_user,is_deleted,crm_mpg_id,is_mpg,manage_id)
SELECT vt.teamid AS id,vt.departid AS dept_id,vt.team AS dept_name,ifnull(vt.parentid_ehr,ced.parentid) AS parent_id,ced.oa_departid AS oa_dept_id,ifnull(ced.ehr_id,vt.ehr_id) AS ehr_id,ifnull(ced.ehr_uid,cu.ehr_uid) AS ehr_uid,ifnull(ced.userid,cu.user_code) AS manage_user,vt.is_delete AS is_deleted,vt.gmid AS crm_mpg_id,ifnull((
SELECT 1 FROM {$crm2DbName}.vtiger_mpg vm WHERE vm.departid=vt.departid),0) AS is_mpg,(
SELECT id FROM crm_users WHERE user_code=ifnull(ced.userid,cu.user_code)) AS manage_id FROM {$crm2DbName}.vtiger_team vt LEFT JOIN {$crm2DbName}.vtiger_user2team u2t ON u2t.teamid=vt.teamid AND u2t.isowner=1 LEFT JOIN crm_users cu ON cu.id=u2t.userid LEFT JOIN {$crm2DbName}.crm_ehr_dept ced ON ced.departid=vt.departid");
            //关闭初始团队
            $team = CrmDeptModel::where('dept_name', '初始团队')->first();
            $team->is_deleted = 1;
            $team->save();

            // 写入mpg数据
            DB::statement("INSERT INTO crm_dept (dept_id,dept_name,parent_id,oa_dept_id,ehr_id,ehr_uid,manage_user,is_deleted,crm_mpg_id,is_mpg,manage_id)
SELECT DISTINCT vm.departid AS dept_id,vm.mpg AS dept_name,vm.parentid AS parent_id,ced.oa_departid AS oa_dept_id,ifnull(vm.ehr_id,ced.ehr_id) AS ehr_id,ifnull(ced.ehr_uid,cu.ehr_uid) AS ehr_uid,ifnull(ced.userid,cu.user_code) AS manage_user,1 AS is_deleted,vm.mpgid AS crm_mpg_id,1 AS is_mpg,(
SELECT id FROM crm_users WHERE user_code=ifnull(ced.userid,cu.user_code)) AS manage_id FROM {$crm2DbName}.vtiger_mpg vm LEFT JOIN crm_users cu ON cu.id=vm.ownerid LEFT JOIN {$crm2DbName}.crm_ehr_dept ced ON ced.departname=vm.mpg WHERE vm.departid NOT IN (
SELECT DISTINCT dept_id FROM crm_dept)");

            // 写入top数据
            DB::statement("INSERT INTO crm_dept (dept_id,dept_name,parent_id,oa_dept_id,ehr_id,ehr_uid,manage_user,is_deleted,crm_mpg_id,is_mpg,manage_id)
SELECT ted.dept_id,ted.dept_name,ted.parent_id,ted.oa_dept_id,ted.ehr_id,ted.ehr_uid,ted.manage_user,ted.deleted,0 AS crm_mpg_id,0 AS is_mpg,(
SELECT id FROM crm_users WHERE user_code=ted.manage_user) AS manage_id FROM {$topDbName}.ehr_dept ted WHERE ted.dept_id NOT IN (
SELECT DISTINCT dept_id FROM crm_dept)");

            //使用top数据填缺缺省的部门信息
            DB::statement("UPDATE crm_dept cd,{$topDbName}.ehr_dept ted SET cd.parent_id=ted.parent_id WHERE cd.dept_id=ted.dept_id AND cd.parent_id IS NULL AND ted.parent_id>0");
            DB::statement("UPDATE crm_dept cd,{$topDbName}.ehr_dept ted SET cd.oa_dept_id=ted.oa_dept_id WHERE cd.dept_id=ted.dept_id AND cd.oa_dept_id IS NULL AND ted.oa_dept_id>0");
            DB::statement("UPDATE crm_dept cd,{$topDbName}.ehr_dept ted
SET cd.ehr_uid=ted.ehr_uid,cd.manage_user=ted.manage_user,cd.manage_id=(
SELECT id FROM crm_users WHERE user_code=ted.manage_user) WHERE cd.dept_id=ted.dept_id AND cd.ehr_uid IS NULL AND ted.ehr_uid> 0");

            $this->info(date("Y-m-d H:i:s") . ' 初始化部门数据完成');
        }

        $this->info(date("Y-m-d H:i:s") . ' 开始同步部门数据');
        $ykService = new YkService();
        $res = $ykService->getYkData('Department');
        if (!$res['success']) {
            $this->error(date("Y-m-d H:i:s") . ' 获取部门信息失败' . $res['message']);
            return;
        }
        $this->info(date("Y-m-d H:i:s") . ' 获取部门信息成功');
        $datas = $res['data'];
        $staffs = [];
        $activeDeptIds = [];
        foreach ($datas as $data) {
            $dept = CrmDeptModel::where('dept_id', $data['departid'])->first();
            if (!$dept) {
                $dept = new CrmDeptModel();
                $dept->dept_id = $data['departid'];
                $dept->dept_name = $data['departname'];
                $dept->oa_dept_id = $data['oa_departid'];
                $dept->ehr_id = $data['ehr_id'];
                $dept->crm_mpg_id = 0;
                $dept->is_mpg = 0;
            }

            if (array_key_exists($data['userid'], $staffs)) {
                $user = $staffs[$data['userid']];
            } else {
                $user = CrmUserModel::where('user_code', $data['userid'])->first();
                $staffs[$data['userid']] = $user;
            }

            $dept->parent_id = $data['parentid'];
            $dept->ehr_uid = $data['ehr_uid'];
            $dept->manage_user = $data['userid'];
            $dept->manage_id = $user->id ?? 0;
            $dept->save();
            $activeDeptIds[] = $data['departid'];
        }
        DB::table('dept')->whereNotIn('dept_id', $activeDeptIds)->update(['is_deleted' => 1]);

        $this->info(date("Y-m-d H:i:s") . ' 同步部门数据完成');

        $this->info(date("Y-m-d H:i:s") . ' 开始生成部门树状 num');
        $CrmDeptService = new CrmDeptService();
        if ($CrmDeptService->generateDeptNum()) {
            $this->info(date("Y-m-d H:i:s") . ' 生成部门树状 num完成');
        } else {
            $this->error(date("Y-m-d H:i:s") . ' 生成部门树状 num失败');
        }

        if ($this->option('initialize')) {
            $this->info(date("Y-m-d H:i:s") . ' 开始更新员工历史数据对应部门信息');
            DB::statement("UPDATE crm_users target,(SELECT cu.id,cd.id AS crm_depart_id,cd.dept_id FROM crm_users cu JOIN crm_dept cd ON cd.ehr_id=cu.ehr_depart_id) raw SET target.crm_depart_id=raw.crm_depart_id WHERE target.id=raw.id");
            $this->info(date("Y-m-d H:i:s") . ' 更新员工历史数据对应部门信息完成');
        }
    }
}
