<?php

namespace App\Console\Commands;

use App\Models\FastQueryModel\FastQueryModel;
use App\Utils\OAUtil;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use phpseclib3\Crypt\RSA;

class LinsDebug extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:lins-debug';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//       $private = RSA::createKey(1024);
//       $public = $private->getPublicKey();
//       $fingerprint = $public->getFingerprint("sha256");
//       $appid = "881ed174-a092-4248-809c-2d4ebd02907b";
//       $url = "https://oa-kf.thinktown.com";
//       $api = "/api/ec/dev/auth/regist";
//
//
//       $doReq = Http::withHeaders([
//           "appid" => $appid,
//           "cpk" => $fingerprint,
//       ])->post($url . $api);
//       dd($doReq->body());
//       $token = OAUtil::doOARequest("/api/workflow/paService/getWorkflowRequest", "5648", ["requestId" => 997513], "GET");

        $data1 = [
            ["fieldName" => ["fieldValue" => ["value" => "5648", "name" => "sqr"]]],
            ["fieldName" => ["fieldValue" => ["value" => "2025-08-23", "name" => "sqrq"]]],
            ["fieldName" => ["fieldValue" => ["value" => "399", "name" => "yhqqfr"]]],
            ["fieldName" => ["fieldValue" => ["value" => 12, "name" => "yhqje"]]],
            ["fieldName" => ["fieldValue" => ["value" => "ababab", "name" => "qkms"]]],
            ["fieldName" => ["fieldValue" => ["value" => "243000108", "name" => "xm"]]],
            ["fieldName" => ["fieldValue" => ["value" => "9999999", "name" => "gjr"]]],
            ["fieldName" => ["fieldValue" => ["value" => "", "name" => "gjrtd"]]],
            ["fieldName" => ["fieldValue" => ["value" => "这是个产品", "name" => "cpmc"]]],
            ["fieldName" => ["fieldValue" => ["value" => "S062625A01", "name" => "cpbh"]]],
            ["fieldName" => ["fieldValue" => ["value" => "202506260002", "name" => "ddbh"]]],
            ["fieldName" => ["fieldValue" => ["value" => 3000.00, "name" => "ddsjzj"]]],
            ["fieldName" => ["fieldValue" => ["value" => "", "name" => "Credential"]]],
            ["fieldName" => ["fieldValue" => ["value" => 1, "name" => "qr"]]],
        ];
       OAUtil::createWorkFlow("5648", 318, "林德标的oa特批", $data1);
    }
}
