<?php

namespace App\Console\Commands;

use App\Http\Service\DepartmentService\CrmUserService;
use ErrorException;
use Exception;
use Illuminate\Console\Command;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class UaSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:ua-sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '获取ua队列数据更新员工数据';

    /**
     * amqp连接获取最新ua用户数据
     * @throws ErrorException
     * @throws Exception
     */
    public function handle(): void
    {
        $arrConfig = config('queue.connections.ua_to_crm3');
        $objConnection = new AMQPStreamConnection(
            $arrConfig['host'],
            $arrConfig['port'],
            $arrConfig['login'],
            $arrConfig['password'],
            $arrConfig['vhost'],
            true,
            'AMQPLAIN',
            'null',
            'en_US',
            '3.0',
            '3.0',
            null,
            true,
            30
        );
        $objChannel = $objConnection->channel();
        $objChannel->confirm_select();
        $objChannel->queue_declare($arrConfig['queue'], false, true, false, false);
        $objChannel->basic_qos(null, 1, null);
        $callback = function ($msg) use ($objChannel) {
            $arrData = json_decode($msg->body, true);
            try {
                switch ($arrData['message_type']) {
                    case 1:
                        new CrmUserService()->syncUser($arrData['data']);
                        break;
                    default:
                }
            } catch (Exception $exception) {
                $this->error(json_encode($exception));
            }
            $msg->delivery_info['channel']->basic_ack($msg->delivery_info['delivery_tag']);
        };
        $objChannel->basic_consume($arrConfig['queue'], '', false, false, false, false, $callback);
        while ($objChannel->is_consuming()) {
            $objChannel->wait();
        }
    }
}
