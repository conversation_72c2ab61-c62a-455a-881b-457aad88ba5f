<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialSchoolCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-school {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化学校数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->option('rollback')) {
            DB::table('grade')->truncate();
            DB::table('course_system')->truncate();
            DB::table('region')->truncate();
            DB::table('school')->truncate();
        } else {
            $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
            // 写入年级
            DB::table('grade')->insert([
                ['grade_name' => '1年级', 'old_id' => '1'],
                ['grade_name' => '2年级', 'old_id' => '2'],
                ['grade_name' => '3年级', 'old_id' => '3'],
                ['grade_name' => '4年级', 'old_id' => '4'],
                ['grade_name' => '5年级', 'old_id' => '5'],
                ['grade_name' => '6年级', 'old_id' => '6'],
                ['grade_name' => '7年级', 'old_id' => '7'],
                ['grade_name' => '8年级', 'old_id' => '8'],
                ['grade_name' => '9年级', 'old_id' => '9'],
                ['grade_name' => '10年级', 'old_id' => '10'],
                ['grade_name' => '11年级', 'old_id' => '11'],
                ['grade_name' => '12年级', 'old_id' => '12'],
                ['grade_name' => '大一', 'old_id' => '14'],
                ['grade_name' => '大二', 'old_id' => '15'],
                ['grade_name' => '大三', 'old_id' => '16'],
                ['grade_name' => '大四', 'old_id' => '17'],
                ['grade_name' => '大五', 'old_id' => '18'],
                ['grade_name' => '研一', 'old_id' => '19'],
                ['grade_name' => '研二', 'old_id' => '20'],
                ['grade_name' => '研三', 'old_id' => '21'],
                ['grade_name' => '研四', 'old_id' => '22'],
                ['grade_name' => '研五', 'old_id' => '23'],
                ['grade_name' => '博一', 'old_id' => '24'],
                ['grade_name' => '博二', 'old_id' => '25'],
                ['grade_name' => '博三', 'old_id' => '26'],
                ['grade_name' => '博四', 'old_id' => '27'],
                ['grade_name' => '博五', 'old_id' => '28'],
                ['grade_name' => '特殊', 'old_id' => '29'],
            ]);

            // 写入课程体系
            DB::table('course_system')->insert([
                ['id' => 1, 'name' => 'AP'],
                ['id' => 2, 'name' => 'A-Level'],
                ['id' => 3, 'name' => 'IB'],
                ['id' => 4, 'name' => 'IGCSE'],
                ['id' => 5, 'name' => '中国大陆课程'],
                ['id' => 6, 'name' => '加拿大课程'],
                ['id' => 7, 'name' => '美高课程'],
                ['id' => 8, 'name' => '香港课程'],
                ['id' => 9, 'name' => '澳洲课程'],
                ['id' => 10, 'name' => '其他国家或地区的课程'],
            ]);

            // 写入地区
            DB::statement("INSERT INTO crm_region (id,name_cn,parent_id,depth) SELECT*FROM {$crm2DbName}.`vtiger_school_region`");

            // 写入学校
            DB::statement("INSERT INTO crm_school (
	id,
	name_cn,
	name_en,
	country_id,
	province_id,
	city_id,
	scope,
	course_system,
	grade,
	region_code,
	is_deleted,
	description,
	created_by,
	updated_by,
	created_at,
	updated_at
) SELECT
s.schoolsid AS id,
s.school_name AS name_cn,
s.school_name_en AS name_en,
s.country AS country_id,
s.province AS province_id,
s.city AS city_id,
CONCAT( '[', s.scope, ']' ) AS scope,
CONCAT( '[', s.course_system_str, ']' ) AS course_system,
CONCAT( '[', ( SELECT GROUP_CONCAT( g.id ) FROM crm_grade g WHERE FIND_IN_SET( g.old_id, s.grade ) > 0 ), ']' ) AS grade,
s.region_code AS region_code,
vcs.deleted AS is_deleted,
s.description,
vcs.smcreatorid AS created_by,
vcs.modifiedby AS updated_by,
vcs.createdtime AS created_at,
vcs.modifiedtime AS updated_at
FROM
	{$crm2DbName}.vtiger_schools s
	JOIN {$crm2DbName}.vtiger_crmentity vcs ON vcs.crmid = s.schoolsid");
        }
    }
}
