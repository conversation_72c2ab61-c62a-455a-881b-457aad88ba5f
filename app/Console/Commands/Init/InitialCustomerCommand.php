<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialCustomerCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-customer {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化客户数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
        if ($this->option('rollback')) {
            DB::table('customer')->truncate();
            DB::table('customer_relation')->truncate();
        } else {
            // 写入客户基础数据
            DB::statement("INSERT INTO crm_customer
SELECT va.accountid,va.ua_id,va.ua_mobile,va.wxid,va.smpid AS `code`,va.accountname AS `name`,va.english_name,va.isconvertedfromlead AS is_from_lead,IF(va.account_type='Male',0,1) AS gender,2 AS customer_stage,va.accountstate AS training_status,va.cf_1652 AS study_status,va.rating AS source,va.sourcedetail AS source_detail,va.sourcememo AS source_memo,IF(va.phone='NULL' OR va.phone='待确认',NULL,va.phone) AS phone,va.contactrelation AS phone_owner,va.contact_1 AS other_contact,va.contact_2 AS other_contact_detail,IF(va.school='',NULL,cast(va.school AS SIGNED)) AS school,(
SELECT id FROM crm_grade WHERE grade_name=va.grade) AS grade,va.school_year,va.is_intention,vaf.cf_1654 AS course_system,va.tendtocollege AS target_school,va.abroadintension AS abroad_intention,va.majorintention AS major_intention,va.isinthinktown AS is_in_xkt,va.otherinstitutions AS other_institution,va.apply_year,va.apply_month AS apply_season,va.lose_reason,va.total_usd,va.total_rmb,va.comm_content AS last_comm,va.next_comm_time,vca.description,vca.deleted AS is_deleted,vca.smcreatorid AS created_by,vca.modifiedby AS updated_by,vca.createdtime AS created_at,vca.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account va JOIN {$crm2DbName}.vtiger_crmentity vca ON vca.crmid=va.accountid JOIN {$crm2DbName}.vtiger_accountscf vaf ON vaf.accountid=va.accountid");
            // 写入客户关系
            DB::statement("INSERT INTO crm_customer_relation (customer_id,user_id,start_time,end_time,memo,type,STATUS,created_by,updated_by,created_at,updated_at)
SELECT va.accountid AS customer_id,vca.smownerid AS user_id,vca.createdtime AS start_date,'2099-12-20 00:00:00' AS end_time,NULL AS memo,1 AS type,1 AS `status`,vca.smcreatorid AS created_by,vca.modifiedby AS updated_by,vca.createdtime AS created_at,vca.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account va JOIN {$crm2DbName}.vtiger_crmentity vca ON vca.crmid=va.accountid UNION ALL
SELECT a2s.accountid AS customer_id,a2s.userid AS user_id,vp.startdate AS start_time,IF(vp.enddate IS NULL OR CAST(vp.enddate AS CHAR(20))='0000-00-00 00:00:00','2099-12-20 00:00:00',vp.enddate) AS end_time,vp.memo AS memo,2 AS `type`,IF(vp.permitstatus='Active',1,0) AS `status`,vcp.smcreatorid AS created_by,vcp.modifiedby AS updated_by,vcp.createdtime AS created_at,vcp.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account2server a2s JOIN {$crm2DbName}.vtiger_permit vp ON vp.permitid=a2s.permitid JOIN {$crm2DbName}.vtiger_crmentity vcp ON vcp.crmid=vp.permitid UNION ALL
SELECT a2c.accountid AS customer_id,a2c.userid AS user_id,vp.startdate AS start_time,IF(vp.enddate IS NULL OR CAST(vp.enddate AS CHAR(20))='0000-00-00 00:00:00','2099-12-20 00:00:00',vp.enddate) AS end_time,vp.memo AS memo,3 AS `type`,IF(vp.permitstatus='Active',1,0) AS `status`,vcp.smcreatorid AS created_by,vcp.modifiedby AS updated_by,vcp.createdtime AS created_at,vcp.modifiedtime AS updated_at FROM {$crm2DbName}.vtiger_account2consultant a2c JOIN {$crm2DbName}.vtiger_permit vp ON vp.permitid=a2c.permitid JOIN {$crm2DbName}.vtiger_crmentity vcp ON vcp.crmid=vp.permitid");
            //写入线索数据
            DB::statement("");
        }

    }
}
