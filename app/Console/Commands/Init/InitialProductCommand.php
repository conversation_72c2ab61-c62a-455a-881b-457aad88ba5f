<?php

namespace App\Console\Commands\Init;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class InitialProductCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:initial-product {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化产品数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $crm2DbName = env('CRM2_DB_NAME', 'cas_crm');
        if ($this->option('rollback')) {
            DB::table('bu')->truncate();
            DB::table('value_chain')->truncate();
            DB::table('value_chain_detail')->truncate();
            DB::table('additional_value_chain')->truncate();
            DB::table('product_category')->truncate();
            DB::table('product_level')->truncate();
            DB::table('pricing_factor_value')->truncate();
            DB::table('product_category_factor')->truncate();
            DB::table('subject')->truncate();
            DB::table('achievement')->truncate();
            DB::table('product_specification')->truncate();
            DB::table('product_specification_factors')->truncate();
            DB::table('product_specification_subjects')->truncate();
            DB::table('product_specification_price')->truncate();
        } else {
            // 写入产品线
            DB::table('bu')->insert([
                ['id' => 3, 'name' => '全合壹', 'is_deleted' => 0],
                ['id' => 4, 'name' => 'SAT/ACT', 'is_deleted' => 0],
                ['id' => 5, 'name' => '国际学科', 'is_deleted' => 0],
                ['id' => 6, 'name' => '托福', 'is_deleted' => 0],
                ['id' => 22, 'name' => '新课堂', 'is_deleted' => 0],
                ['id' => 23, 'name' => '外教国际学科', 'is_deleted' => 0],
                ['id' => 24, 'name' => '课外活动', 'is_deleted' => 0],
                ['id' => 25, 'name' => '编程', 'is_deleted' => 0],
                ['id' => 26, 'name' => 'Apolish', 'is_deleted' => 0],
                ['id' => 27, 'name' => '学术顾问计划', 'is_deleted' => 0],
                ['id' => 28, 'name' => 'ABC咨询课程', 'is_deleted' => 0],
                ['id' => 29, 'name' => '学术顾问计划2.0', 'is_deleted' => 0],
            ]);
            // 写入价值链
            DB::table('value_chain')->insert([
                ['id' => 1, 'name' => 'A', 'is_deleted' => 0],
                ['id' => 2, 'name' => 'B', 'is_deleted' => 0],
                ['id' => 3, 'name' => 'C', 'is_deleted' => 0],
                ['id' => 4, 'name' => 'Other', 'is_deleted' => 0]
            ]);
            // 写入价值链详情
            DB::table('value_chain_detail')->insert([
                ['id' => 1, 'value_chain_id' => 1, 'sign' => 0.15, 'teach' => 0.60, 'service' => 0.25, 'start_date' => '2010-01-01', 'end_date' => '2020-08-20',],
                ['id' => 2, 'value_chain_id' => 2, 'sign' => 0.15, 'teach' => 0.80, 'service' => 0.05, 'start_date' => '2010-01-01', 'end_date' => '2020-08-20',],
                ['id' => 3, 'value_chain_id' => 3, 'sign' => 0.15, 'teach' => 0.70, 'service' => 0.15, 'start_date' => '2010-01-01', 'end_date' => '2020-08-20',],
                ['id' => 4, 'value_chain_id' => 4, 'sign' => 0.00, 'teach' => 0.00, 'service' => 0.00, 'start_date' => '2010-01-01', 'end_date' => '2099-12-31',],
                ['id' => 5, 'value_chain_id' => 1, 'sign' => 0.20, 'teach' => 0.55, 'service' => 0.25, 'start_date' => '2020-08-21', 'end_date' => '2024-12-20',],
                ['id' => 6, 'value_chain_id' => 2, 'sign' => 0.30, 'teach' => 0.60, 'service' => 0.10, 'start_date' => '2020-08-21', 'end_date' => '2024-12-20',],
                ['id' => 7, 'value_chain_id' => 3, 'sign' => 0.30, 'teach' => 0.50, 'service' => 0.20, 'start_date' => '2020-08-21', 'end_date' => '2024-12-20',],
                ['id' => 8, 'value_chain_id' => 1, 'sign' => 0.27, 'teach' => 0.45, 'service' => 0.28, 'start_date' => '2024-12-21', 'end_date' => '2025-12-20',],
                ['id' => 9, 'value_chain_id' => 2, 'sign' => 0.35, 'teach' => 0.50, 'service' => 0.15, 'start_date' => '2024-12-21', 'end_date' => '2025-12-20',],
                ['id' => 10, 'value_chain_id' => 3, 'sign' => 0.33, 'teach' => 0.45, 'service' => 0.22, 'start_date' => '2024-12-21', 'end_date' => '2025-12-20',],
            ]);
            // 写入额外价值链
            DB::table('additional_value_chain')->insert([
                ['id' => 1, 'name' => '新生首单', 'code' => 'newAccount', 'version' => 1, 'sign' => 0.10, 'teach' => -0.10, 'service' => 0.00, 'sort_order' => 200, 'start_date' => '2020-08-21', 'end_date' => '2099-12-31', 'memo' => '首次在新课堂签约', 'ext' => "{}"],
                ['id' => 2, 'name' => '跨产品线', 'code' => 'crossBu', 'version' => 1, 'sign' => 0.05, 'teach' => -0.05, 'service' => 0.00, 'sort_order' => 100, 'start_date' => '2020-08-21', 'end_date' => '2024-12-20', 'memo' => '跨产品线签约', 'ext' => '{"blacklist": {"bu": [28, 29]}}'],
                ['id' => 3, 'name' => '支持新地区', 'code' => 'newCampus', 'version' => 1, 'sign' => 0.15, 'teach' => -0.15, 'service' => 0.00, 'sort_order' => 300, 'start_date' => '2020-08-21', 'end_date' => '2024-12-20', 'memo' => '支持新地区的自地区签约', 'ext' => '{"blacklist": {"bu": [28, 29]}}'],
                ['id' => 4, 'name' => '新产品', 'code' => 'newProduct', 'version' => 1, 'sign' => 0.10, 'teach' => -0.10, 'service' => 0.00, 'sort_order' => 400, 'start_date' => '2023-01-10', 'end_date' => '2023-12-20', 'memo' => '内部阶段性新产品推广', 'ext' => '{"range": []}']
            ]);
            // 写入产品类别
            DB::statement("INSERT INTO crm_product_category (id,name_cn,name_en,type,memo,`status`,value_chain_id,bu_id,created_by,created_at) SELECT c.categoryid,c.name_cn,c.name_en,c.type,c.memo,c.`status`,c.valuechainid,c.bu,c.createdby,c.createtime FROM {$crm2DbName}.crm_product_category c");

            // 写入产品业务分级
            DB::statement("INSERT INTO crm_product_level (id,category_id,name_cn,name_en,`status`) SELECT c.levelid,c.categoryid,c.name_cn,c.name_en,c.`status` FROM {$crm2DbName}.crm_product_level c");

            // 写入定价因素值
            DB::table('pricing_factor_value')->insert([
                ['id' => 1, 'pricing_factor_id' => 1, 'name_en' => 'Hangzhou', 'name_cn' => '杭州', 'status' => 1],
                ['id' => 2, 'pricing_factor_id' => 1, 'name_en' => 'NYC', 'name_cn' => '纽约', 'status' => 1],
                ['id' => 3, 'pricing_factor_id' => 1, 'name_en' => 'Shanghai', 'name_cn' => '上海', 'status' => 1],
                ['id' => 4, 'pricing_factor_id' => 1, 'name_en' => 'Shenzhen', 'name_cn' => '深圳', 'status' => 1],
                ['id' => 5, 'pricing_factor_id' => 1, 'name_en' => 'Beijing', 'name_cn' => '北京', 'status' => 1],
                ['id' => 6, 'pricing_factor_id' => 1, 'name_en' => 'Global', 'name_cn' => '全球', 'status' => 1],
                ['id' => 7, 'pricing_factor_id' => 1, 'name_en' => 'None', 'name_cn' => '无', 'status' => 1],
                ['id' => 8, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '1V1', 'status' => 1],
                ['id' => 9, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '1V6', 'status' => 1],
                ['id' => 10, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '大班', 'status' => 1],
                ['id' => 11, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '在线1V1', 'status' => 1],
                ['id' => 12, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '1V2', 'status' => 1],
                ['id' => 13, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '在线1V2', 'status' => 1],
                ['id' => 14, 'pricing_factor_id' => 2, 'name_en' => '', 'name_cn' => '在线班课', 'status' => 1],
                ['id' => 15, 'pricing_factor_id' => 2, 'name_en' => 'Other', 'name_cn' => '其他', 'status' => 1],
                ['id' => 16, 'pricing_factor_id' => 3, 'name_en' => 'None', 'name_cn' => '无', 'status' => 1],
                ['id' => 17, 'pricing_factor_id' => 3, 'name_en' => '', 'name_cn' => '高级教师', 'status' => 1],
                ['id' => 18, 'pricing_factor_id' => 3, 'name_en' => '', 'name_cn' => '特级教师', 'status' => 1],
                ['id' => 19, 'pricing_factor_id' => 3, 'name_en' => '', 'name_cn' => '明星教师', 'status' => 1],
                ['id' => 20, 'pricing_factor_id' => 3, 'name_en' => 'Other', 'name_cn' => '其他', 'status' => 1],
                ['id' => 21, 'pricing_factor_id' => 4, 'name_en' => 'None', 'name_cn' => '无', 'status' => 1],
                ['id' => 22, 'pricing_factor_id' => 4, 'name_en' => 'B', 'name_cn' => 'B', 'status' => 1],
                ['id' => 23, 'pricing_factor_id' => 4, 'name_en' => 'C', 'name_cn' => 'C', 'status' => 1],
                ['id' => 24, 'pricing_factor_id' => 4, 'name_en' => 'D', 'name_cn' => 'D', 'status' => 1],
                ['id' => 25, 'pricing_factor_id' => 4, 'name_en' => 'E', 'name_cn' => 'E', 'status' => 1],
            ]);

            // 写入产品类别定价因素
            DB::statement("INSERT INTO crm_product_category_factor (id,category_id,pricing_factor_id) SELECT c.categorypricingfactorid,c.categoryid,c.pricingfactorid FROM {$crm2DbName}.crm_product_category_factor c");

            // 写入科目
            DB::statement("INSERT INTO crm_subject (id,name_cn,name_en,`status`) SELECT c.itemid,c.name_cn,c.name_en,`status` FROM {$crm2DbName}.crm_item c");

            // 写入绩效类型
            DB::table('achievement')->insert([
                ['id' => 1, 'name' => '无', 'status' => 1],
                ['id' => 2, 'name' => '托福', 'status' => 1],
                ['id' => 3, 'name' => 'SAT', 'status' => 1],
                ['id' => 4, 'name' => '国际学科', 'status' => 1],
                ['id' => 5, 'name' => '咨询', 'status' => 1],
            ]);

            // 写入产品规格
            DB::statement("INSERT INTO crm_product_specification (id,category_id,`code`,name_cn,name_en,memo,exceeding,currency,`status`,`level_id`,achievement_id,custom_price,price_type,static_quantity,created_by,updated_by,created_at)
SELECT c.specificationid,c.categoryid,c.`code`,c.name_cn,c.name_en,c.memo,ifnull(c.exceeding,0),c.currency,c.`status`,c.levelid,c.achievementid,c.customprice,c.price_type,c.static_quantity,c.createdby,c.createdby,c.createtime FROM {$crm2DbName}.crm_product_specification c");

            // 写入产品规格定价因素
            DB::statement("INSERT INTO crm_product_specification_factors (pricing_factor_id,specification_id,`value_id`) SELECT c.pricingfactorid,c.specificationid,c.valueid FROM {$crm2DbName}.crm_product_specification_detail c WHERE c.specificationid !=0");

            // 写入产品规格科目
            DB::statement("INSERT INTO crm_product_specification_subjects (specification_id,`subject_id`,quantity,nct_quantity) SELECT c.specificationid,c.itemid,c.quantity,c.nct_quantity FROM {$crm2DbName}.crm_product_specification_item c WHERE c.specificationid !=0 AND c.`status` = 1");

            // 写入产品规格价格
            DB::statement("INSERT INTO crm_product_specification_price (id,specification_id,price,start_date,end_date,`status`,created_by,created_at) SELECT c.pricedetailid,c.specificationid,c.price,c.starttime,c.endtime,c.`status`,1,c.createtime FROM {$crm2DbName}.crm_product_specification_price c");
        }
    }
}
