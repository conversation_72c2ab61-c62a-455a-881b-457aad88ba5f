<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->string('slug')->nullable();
            $table->string("http_method")->nullable();
            $table->string("http_path")->nullable();
            $table->string("type")->default("api")->comment("权限类型 api menu");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('permissions', function (Blueprint $table) {
            $table->dropColumn('slug');
            $table->dropColumn('http_method');
            $table->dropColumn('http_path');
            $table->dropColumn('type');
        });
    }
};
