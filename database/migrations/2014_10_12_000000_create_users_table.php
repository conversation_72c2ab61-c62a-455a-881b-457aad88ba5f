<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->increments('id')->comment('crmid');
            $table->string('name')->comment('姓名');
//            $table->string('email')->unique();
//            $table->timestamp('email_verified_at')->nullable();
//            $table->string('password');
//            $table->rememberToken();
            $table->timestamps();
            // Additional fields for the user table
            $table->string('user_code')->comment('工号');
            $table->string('ua_id')->comment('uaid');
            $table->integer('is_admin')->default(0)->comment('是否管理员');
            $table->integer('dept_id')->nullable()->comment('企业微信部门id');
            $table->string('position', 255)->nullable()->comment('岗位');
            $table->integer('oa_id')->default(0)->comment('oaid');
            $table->integer('parent_oa_id')->default(0)->comment('oa上级id');
            $table->string('reset_day', 255)->nullable()->comment('休息日');
            $table->integer('oa_dept_id')->default(0)->comment('oa部门id');
            $table->integer('ehr_depart_id')->nullable()->comment('ehr部门id');
            $table->bigInteger('ehr_uid')->nullable()->comment('ehr的id');
            $table->bigInteger('ehr_puid')->nullable()->comment('ehr的人员上级id');
            $table->string('user_num', 255)->default('')->comment('下级树状num');
            $table->tinyInteger('user_status')->default(1)->comment('在职状态');
            $table->integer('campus')->nullable()->comment('校区');
            $table->integer('region')->nullable()->comment('地区');
            $table->integer('employ_type')->nullable()->comment('入职类型');
            $table->string('staff_mail', 255)->default('')->comment('邮箱');

            $table->charset = 'utf8';
            $table->collation = 'utf8_general_ci';
            $table->comment = '用户';
            $table->engine = 'InnoDB';
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
