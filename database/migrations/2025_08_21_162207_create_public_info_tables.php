<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organization', function (Blueprint $table) {
            $table->id();
            $table->string('name', 254)->nullable();
            $table->string('phone', 254)->nullable()->comment('联系电话');
            $table->string('address', 254)->nullable()->comment('地址');
            $table->tinyInteger('if_ea')->nullable()->comment('是否电子签');
            $table->integer('status')->nullable();
            $table->string('ea_phone', 254)->nullable()->comment('电子签联系电话');
            $table->tinyInteger('if_financial_set')->nullable()->comment('是否允许财务收款配置中设置');
            $table->comment('公司信息');
        });

        Schema::create('organization_region',function(Blueprint $table){
            $table->id();
            $table->string('name_cn', 254)->nullable()->comment('中文名');
            $table->string('name_en', 254)->nullable()->comment('英文名');
            $table->integer('status')->default(1)->comment('状态, 0停用1启用');
            $table->string('description', 254)->nullable()->comment('描述');
            $table->comment('公司区域');
        });

        Schema::create('campus',function(Blueprint $table){
            $table->id();
            $table->string('name', 254)->nullable()->comment('中文名');
            $table->integer('region_id')->comment('区域id');
            $table->integer('status')->default(1)->comment('状态, 0停用1启用');
            $table->string('description', 254)->nullable()->comment('描述');
            $table->integer('oa_school_id')->nullable()->comment('oa校区id');
            $table->comment('校区');
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organization');
        Schema::dropIfExists('organization_region');
        Schema::dropIfExists('campus');

    }
};
