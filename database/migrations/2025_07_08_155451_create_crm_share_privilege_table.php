<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('share_privilege', function (Blueprint $table) {
            $table->id();
            $table->integer('share_type')->comment('权限类型 0额外权限 1数据分享');
            $table->integer('obj_type')->comment('对象类型 1人-人 2人及下属-人 3部门-人 4部门及下属部门-人 5人-部门 6人及下属-部门 7部门-部门 8部门及下属部门-部门');
            $table->json('from_ids')->comment('分享方id');
            $table->json('to_ids')->comment('被分享方id');
            $table->date('start_date')->nullable()->comment('开始日期');
            $table->date('end_date')->nullable()->comment('结束日期');
            $table->integer('status')->default(1)->comment('状态 1启用 0禁用');
            $table->json('roles')->nullable()->comment('权限业务角色 1跟进人2维护人3教育顾问');
            $table->json('ext')->nullable()->comment('额外配置');
            $table->text('memo')->nullable()->comment('备注');
            $table->integer('created_by')->comment('创建人');
            $table->integer('updated_by')->comment('更新人');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('share_privilege');
    }
};
