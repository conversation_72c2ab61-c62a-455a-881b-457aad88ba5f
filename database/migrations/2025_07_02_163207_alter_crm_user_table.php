<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('wx_avatar', 255)->nullable()->comment('微信头像');
            $table->integer('crm_depart_id')->nullable()->comment('crm的部门id');
            if (Schema::hasColumn('users', 'dept_id')) {
                $table->dropColumn('dept_id');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasColumn('users', 'wx_avatar')) {
                $table->dropColumn('wx_avatar');
            }
            if (Schema::hasColumn('users', 'crm_depart_id')) {
                $table->dropColumn('crm_depart_id');
            }
            if (!Schema::hasColumn('users', 'dept_id')) {
                $table->integer('dept_id')->nullable()->comment('企业微信部门id');
            }
        });
    }
};
