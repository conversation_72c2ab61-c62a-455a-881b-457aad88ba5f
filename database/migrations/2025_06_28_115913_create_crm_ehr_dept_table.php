<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('dept', function (Blueprint $table) {
            $table->id()->comment('主键,用原有crm的teamid顶上去');
            $table->integer('dept_id')->nullable()->comment('企业微信部门id');
            $table->string('dept_name', 255)->nullable()->comment('部门名称');
            $table->integer('parent_id')->nullable()->comment('企业微信上级部门id');
            $table->string('dept_num', 255)->nullable()->comment('部门编号（用于快速查询下级）');
            $table->integer('oa_dept_id')->nullable()->default(0)->comment('oa部门id');
            $table->integer('ehr_id')->nullable()->comment('ehr部门id');
            $table->integer('ehr_uid')->nullable()->comment('负责人ehrid');
            $table->string('manage_user')->nullable()->comment('负责人工号');
            $table->integer('is_deleted')->default(0)->comment('是否删除');
            $table->integer('depth')->nullable()->comment('组织树深度');
            $table->integer('crm_mpg_id')->nullable()->comment('对应原有crm的mpgid');
            $table->integer('is_mpg')->default(0)->comment('是否为mpg');
            $table->integer('manage_id')->nullable()->comment('负责人crmid');
            $table->timestamps();

            $table->comment('组织架构落地表');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('dept');
    }
};
