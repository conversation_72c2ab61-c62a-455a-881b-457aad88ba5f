APP_NAME=CRM3
APP_ENV=local
APP_KEY=base64:2AqYS9ijTTy4x0dxsYdsDQvuIsdcDRgoGdZ3CzEcrvc=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# 开发数据库连接
DB_CONNECTION=mysql
DB_HOST=************
DB_PORT=3306
DB_DATABASE=crm3
DB_USERNAME=cas_crm_adm
DB_PASSWORD='Xkt#2021'

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

# Redis 配置
# 开发环境
REDIS_HOST=************
REDIS_PASSWORD='Xkt2021!@#'
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


# CRM3 业务相关配置

# SSO 配置
SSO_COOKIE_KEY=DEV_UA_TOKEN
SSO_COOKIE_TIME=720
SSO_COOKIE_DOMAIN=local.com
#开发环境
SSO_SIGNATURE_KEY=nBZ7K6miwEjABoKP25JBbcf8zyA9JXTcaET2PWUhWzbygxA5eH7KHBgLmyExtkJK
#测试环境
#SSO_SIGNATURE_KEY=qUEytOB4QbhlWhMZhDBGdLlME9oURRbOiWqzrku8I16KrMmxETCGvPNcDVGcjIJj


YK_BASE_URL=https://yk.xkt.com

# 连通的其他数据库配置
TOP_DB_NAME=top
CRM2_DB_NAME=cas_crm

#ua amqp配置
UA_RABBITMQ_HOST=************
UA_RABBITMQ_VHOST='/'
UA_RABBITMQ_LOGIN=admin
UA_RABBITMQ_PASSWORD='Xkt#2021'
UA_RABBITMQ_EXCHANGE_NAME=dev_to_all_platform
UA_RABBITMQ_EXCHANGE_TYPE=fanout
UA_RABBITMQ_QUEUE=dev_ua_to_crm3

UA_URL=https://ua-kf.thinktown.com:8443

OA_URL=https://oa-kf.thinktown.com
OA_APPID=881ed174-a092-4248-809c-2d4ebd02907b
OA_TOKEN_TIME=3600
