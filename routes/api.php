<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('ua')->get('/version', function () {
    return response()->json([
        'version' => 'V3.0.0_alpha',
        'patch_version' => '20250619',
        'modified_database' => '',
    ]);
});

//前端对接用接口

Route::group(['namespace' => 'App\Http\Controllers\PublicControllers', 'middleware' => 'ua'], function ($router) {
    //学校
    $router->resource('school', 'CrmSchoolController', ['only' => ['index', 'show']]);
});

Route::group(['namespace' => 'App\Http\Controllers\EntityControllers', 'middleware' => ['ua', 'crm.permission']], function ($router) {
    //学员
    $router->resource('customer', 'CrmCustomerController');
});


//其他平台对接用接口
